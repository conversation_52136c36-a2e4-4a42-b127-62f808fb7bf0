{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/css/Home/Switch.scss.css"], "sourcesContent": [".form-check-input{width:72px !important;height:34px !important;border-radius:30px !important;position:relative;background-color:#fff !important;border:none !important;cursor:pointer;appearance:none;transition:background-color .3s ease}.form-check-input::before{content:\"\";position:absolute;width:30px;height:30px;top:2px;left:2px;background-color:#fff;border-radius:50%;transition:transform .3s ease-in-out;box-shadow:0 1px 4px rgba(0,0,0,.3)}.form-check-input:checked{background-color:#00adef !important}.form-check-input:checked::before{transform:translateX(38px)}.form-check-input:focus{box-shadow:none !important}"], "names": [], "mappings": "AAAA;;;;;;;;;;;;AAA2O;;;;;;;;;;;;;AAAgN;;;;AAA8D;;;;AAA6D"}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}