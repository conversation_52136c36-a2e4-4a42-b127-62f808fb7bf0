{"kind": "FETCH", "data": {"headers": {"cache-control": "no-cache, private", "connection": "close", "content-type": "application/json", "date": "Mon, 28 Jul 2025 09:16:26 GMT, Mon, 28 Jul 2025 09:16:25 GMT", "host": "127.0.0.1:8000", "vary": "Origin", "x-powered-by": "PHP/8.2.4", "x-ratelimit-limit": "500", "x-ratelimit-remaining": "499"}, "body": "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", "status": 200, "url": "http://127.0.0.1:8000/api/v1/featured-resource"}, "revalidate": 3600, "tags": []}