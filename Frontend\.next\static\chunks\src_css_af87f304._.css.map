{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/CommonButton.scss.css"], "sourcesContent": [":root{--font-gilroy: \"<PERSON><PERSON>\", sans-serif}.btn-style,.btn-primary{min-height:66px;display:inline-flex;justify-content:center;align-items:center;text-align:center;border-radius:10rem;padding:.5rem 1.5rem;font-size:1.25rem;font-weight:600;background-color:#00adef;border:0;text-transform:capitalize;transition:all ease-in-out .3s;min-width:150px;color:#fff}.btn-style span,.btn-primary span{line-height:1}@media(max-width: 1599px){.btn-style,.btn-primary{min-height:66px}}@media(max-width: 1199px){.btn-style,.btn-primary{min-height:56px;font-size:1.125rem;font-weight:500}}@media(max-width: 767px){.btn-style,.btn-primary{min-height:46px;font-size:1rem}}.btn-style:hover,.btn-primary:hover{background-color:#0099d1;color:#fff}.btn-style.transparent,.btn-primary.transparent{background-color:rgba(0,0,0,0);border:none}.btn-style.white-btn,.btn-primary.white-btn{background:#fff;color:#000}.btn-style.white-btn:hover,.btn-primary.white-btn:hover{background:#00adef;color:#fff}.btn-style.yellow-btn,.btn-primary.yellow-btn{background-color:#fea500;color:#fff}.btn-style.yellow-btn:hover,.btn-primary.yellow-btn:hover{background-color:#c9870d;color:#fff}.btn-style.gray-btn,.btn-primary.gray-btn{background-color:#5e6165 !important;color:#fff}.btn-style.gray-btn:hover,.btn-primary.gray-btn:hover{background-color:#708090;color:#fff}.btn-style.gradient-btn,.btn-primary.gradient-btn{background:linear-gradient(75deg, #00aeef, #1f5aff 50.31%, #da00ff);color:#fff}.btn-style.gradient-btn:hover,.btn-primary.gradient-btn:hover{background:linear-gradient(75deg, #0043ff, #1f5aff 50.31%, #da00ff);color:#fff}.btn-style.green-btn,.btn-primary.green-btn{background-color:#32cd33;color:#fff}.btn-style.green-btn:hover,.btn-primary.green-btn:hover{background-color:#2bb72b;color:#fff}.btn-style.red-btn,.btn-primary.red-btn{background-color:#ff696a;color:#fff}.btn-style.red-btn:hover,.btn-primary.red-btn:hover{background-color:#e65f60;color:#fff}.btn-style.border-btn,.btn-primary.border-btn{background:rgba(0,0,0,0);color:#fff;border:1px solid #00adef}.btn-style.border-btn:hover,.btn-primary.border-btn:hover{background:#00adef;color:#fff}.btn-style .onlyIcon,.btn-primary .onlyIcon{margin-right:15px;display:inline-flex}.btn-style:disabled,.btn-style.disabled,.btn-primary:disabled,.btn-primary.disabled{background:#c5c5d5;color:#fff;cursor:not-allowed;opacity:1}:disabled,.disabled{background-color:#414c60;color:#fff;cursor:not-allowed;opacity:1}.white20{background-color:hsla(0,0%,100%,.1215686275);width:100%}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;;;;;;;;;;;;;;;AAAyT;;;;AAAgD;EAA0B;;;;;AAAyC;EAA0B;;;;;;;AAA4E;EAAyB;;;;;;AAAwD;;;;;AAAwE;;;;;AAA2F;;;;;AAAuE;;;;;AAAsF;;;;;AAAkF;;;;;AAA8F;;;;;AAAyF;;;;;AAA0F;;;;;AAAiI;;;;;AAA6I;;;;;AAAgF;;;;;AAA4F;;;;;AAA4E;;;;;AAAwF;;;;;;AAA2G;;;;;AAAwF;;;;;AAAkF;;;;;;;AAA+I;;;;;;;AAAqF", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/Header.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}header{position:sticky;top:0;left:0;z-index:9998}.home-page .siteHeader{background-color:#000 !important;border-bottom:0}@media(min-width: 1200px){.home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle.show,.home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle:hover{background-color:#2a2e39 !important;color:#fff}.home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle.show::after,.home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle:hover::after{transform:rotate(180deg)}}.home-page .siteHeader .navMenu .common_dropdown.dropdown.show .dropdown-toggle::after{transform:rotate(180deg)}@media(min-width: 1200px){.home-page .siteHeader .navMenu .common_dropdown .dropdown-menu{background-color:#1e222d !important}}.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item:hover,.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.active,.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item:focus{background-color:#2a2e39 !important}.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon{font-weight:700;background:linear-gradient(to right, #2a2e39, #1e222d) !important;color:#00b9ff;transition:none}.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon svg path{fill:#00b9ff}.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover,.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active{background:#2a2e39 !important}@media(max-width: 1199px){.home-page .siteHeader .navbar-collapse .nav-link.white_stroke_icon{font-weight:700;background:linear-gradient(to right, #000000, #2d2d2d) !important;color:#00aeef;transition:none}.home-page .siteHeader .navbar-collapse .nav-link.white_stroke_icon svg path{fill:#00b9ff}.home-page .siteHeader .navbar-collapse{background-color:rgba(0,0,0,.9) !important}}@media(width >= 1200px){.home-page .siteHeader .navbar-collapse .nav-link:hover,.home-page .siteHeader .navbar-collapse .nav-link.active,.home-page .siteHeader .navbar-collapse .nav-link:focus{background-color:#2a2e39 !important;color:#fff}.home-page .languageDropdown{width:64px}}@media(width >= 1200px)and (max-width: 1199px){.home-page .languageDropdown{width:100%}.home-page .languageDropdown .common_dropdown{width:100%}}@media(width >= 1200px){.home-page .languageDropdown .common_dropdown .nav-link:hover,.home-page .languageDropdown .common_dropdown .nav-link.active,.home-page .languageDropdown .common_dropdown .nav-link:focus{color:#fff;background-color:#2a2e39 !important}.home-page .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover,.home-page .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active{background:#2a2e39 !important}}.siteHeader{height:80px;padding:1rem 0;display:flex;align-items:center;justify-content:center;backdrop-filter:blur(10px);background-color:#031940;border-bottom:1px solid #064197}.siteHeader .btn-style{min-height:56px;min-width:169px}@media(max-width: 1199px){.siteHeader .btn-style{min-height:40px;min-width:120px;padding:8px 1rem;font-size:14px}}@media(max-width: 575px){.siteHeader .btn-style{min-height:34px;min-width:80px;font-size:14px}}@media(max-width: 1199px){.siteHeader{z-index:9999;backdrop-filter:none}}@media(max-width: 767px){.siteHeader{padding:.625rem 0}}.siteHeader .navbar{padding:0;width:100%}.siteHeader .navbar .brandLogo img{max-width:190px;width:100%}@media(max-width: 767px){.siteHeader .navbar .brandLogo img{max-width:150px;margin-right:0rem}}@media(max-width: 360px){.siteHeader .navbar .brandLogo img{max-width:120px;margin-right:0rem}}.siteHeader .navbar-collapse{height:auto !important}.siteHeader .navbar-collapse .nav-link{font-size:1.25rem;font-weight:400;background-color:rgba(0,0,0,0);display:flex;align-items:center;white-space:nowrap;padding:.5rem 1.5rem;color:#fff}.siteHeader .navbar-collapse .nav-link:hover,.siteHeader .navbar-collapse .nav-link.active,.siteHeader .navbar-collapse .nav-link:focus{color:#00adef}@media(min-width: 1200px){.siteHeader .navbar-collapse .nav-link:hover,.siteHeader .navbar-collapse .nav-link.active,.siteHeader .navbar-collapse .nav-link:focus{background-color:#283f67 !important;color:#fff}.siteHeader .navbar-collapse .nav-link{margin:0 3px}}@media(max-width: 1199px){.siteHeader .navbar-collapse .nav-link{padding:1.25rem 0rem;border-bottom:1px solid hsla(0,0%,100%,.2);font-size:1.125rem}.siteHeader .navbar-collapse .nav-link img{width:22px}.siteHeader .navbar-collapse .nav-link.white_stroke_icon{font-weight:700;background:linear-gradient(to right, #031940, #283f67);color:#00aeef;transition:none}.siteHeader .navbar-collapse .nav-link.white_stroke_icon svg path{fill:#00b9ff}.siteHeader .navbar-collapse{position:fixed;left:-350px;top:0px;background-color:rgba(3,25,64,.9);backdrop-filter:blur(5px);width:350px;padding:1.25rem 1rem;display:block;transition:all ease-in-out .2s;height:100vh !important;z-index:9999;padding:0}.siteHeader .navbar-collapse a{display:flex;justify-content:flex-start;text-align:left}.siteHeader .navbar-collapse.show{left:0;height:100vh}.siteHeader .navbar-collapse .navMenu{padding:20px;height:calc(100vh - 90px);overflow-y:auto}}@media(max-width: 767px){.siteHeader .navbar-collapse{left:-100%;width:100%}}.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle{padding:.5rem 1.5rem !important;border-radius:0}@media(max-width: 1199px){.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle{padding:1.25rem 0rem !important;border-bottom:1px solid hsla(0,0%,100%,.2);width:100%}}.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle::after{display:block;background-image:url(\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drop-arrow.svg\");background-repeat:no-repeat;background-size:1.15rem;background-position:center;width:1.15rem;height:1.15rem;border:0;transition:all ease-in-out .3s;margin-left:1rem}@media(max-width: 1199px){.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle::after{margin-left:0;position:absolute;right:0}}@media(min-width: 1200px){.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle.show,.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:hover{background-color:#283f67;color:#fff}.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle.show::after,.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:hover::after{transform:rotate(180deg)}}.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown.show .dropdown-toggle::after{transform:rotate(180deg)}@media screen and (max-width: 1199px){.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu{position:static;border:0;background-color:rgba(0,0,0,0);padding:0}}.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu .nav-link{padding:.875rem 1.5rem;align-items:start;font-weight:400 !important}@media screen and (max-width: 1199px){.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu .nav-link{padding:.875rem 1rem}}.siteHeader .navbar .navbar-toggler{background-color:rgba(0,0,0,0);margin-left:0;padding:0;position:relative;width:24px;height:18px}.siteHeader .navbar .navbar-toggler:focus{box-shadow:none}@media(max-width: 1199px){.siteHeader .navbar .navbar-toggler{margin-right:13px}}@media(max-width: 767px){.siteHeader .navbar .navbar-toggler{margin-right:13px}}.siteHeader .navbar .navbar-toggler::after{content:\"\";position:absolute;bottom:0;left:0;width:24px;background-color:#fff;height:2px;transition:all ease-in-out .3s}.siteHeader .navbar .navbar-toggler::before{content:\"\";position:absolute;top:0;left:0;width:24px;background-color:#fff;height:2px;transition:all ease-in-out .3s}.siteHeader .navbar .navbar-toggler .navbar-toggler-icon{background-image:none;height:2px;background-color:#fff;width:24px;transition:all ease-in-out .3s;display:flex}.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle{padding:.5rem .2rem !important;color:#fff;border:0;border-radius:.625rem;font-size:1.25rem;padding:0;display:flex;align-items:center}@media(max-width: 991px){.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle{font-size:1.125rem}}.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle::after{display:none}.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle.show svg path{fill:#00adef}.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu{border-radius:.625rem;border:1px solid hsla(0,0%,100%,.3);min-width:200px;position:absolute;top:45px}@media screen and (max-width: 1199px){.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu{position:static;padding:0;min-width:100%}}.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item{font-size:1.125rem;font-weight:600;padding:.625rem 1rem;color:#fff}@media(max-width: 991px){.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item{font-size:1rem}}.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item svg,.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item img{margin-right:10px}.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item:hover,.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.active,.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item:focus{background:#283f67}.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon{font-weight:700;background:linear-gradient(to right, #283f67, #031940);color:#00b9ff;transition:none}.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon svg path{fill:#00b9ff}.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover,.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active{background:#283f67 !important}.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu.show svg,.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu.show img{width:18px}@media screen and (max-width: 1199px){.siteHeader .navbar .openmenuSidebar{border-bottom:1px solid hsla(0,0%,100%,.5);padding:30px 15px}.siteHeader .navbar .openmenuSidebar .brandLogo{padding:0}.siteHeader .navbar .openmenuSidebar .brandLogo img{max-width:150px}.siteHeader .navbar .openmenuSidebar .navbar-toggler{position:absolute;right:15px}}.siteHeader.openmenu .navbar .navbar-toggler::after{transform:rotate(45deg) translate(-5px, -5px);background-color:#fff}.siteHeader.openmenu .navbar .navbar-toggler::before{transform:rotate(-45deg) translate(-5px, 5px);background-color:#fff}.siteHeader.openmenu .navbar .navbar-toggler .navbar-toggler-icon{opacity:0}.siteHeader .user_icon img{width:26px;height:26px}@media screen and (max-width: 767px){.siteHeader .sidebar_backdrop{display:none}}.languageDropdown{width:64px}@media(max-width: 1199px){.languageDropdown{width:100%}.languageDropdown .common_dropdown{width:100%}}.languageDropdown .common_dropdown .nav-link:hover,.languageDropdown .common_dropdown .nav-link.active,.languageDropdown .common_dropdown .nav-link:focus{color:#fff;background-color:#283f67 !important}.languageDropdown .common_dropdown.dropdown .dropdown-toggle{color:#fff;border:0;border-radius:0 !important;font-size:1.25rem;padding:0;display:flex;align-items:center}@media(max-width: 991px){.languageDropdown .common_dropdown.dropdown .dropdown-toggle{font-size:1rem}}.languageDropdown .common_dropdown.dropdown .dropdown-toggle svg{margin-right:10px}.languageDropdown .common_dropdown.dropdown .dropdown-toggle:focus,.languageDropdown .common_dropdown.dropdown .dropdown-toggle:hover{background-color:rgba(0,0,0,0) !important}@media(max-width: 1199px){.languageDropdown .common_dropdown.dropdown .dropdown-toggle{width:100%}}.languageDropdown .globalIcon .icon{transition:opacity .3s ease}.languageDropdown .globalIcon .blue{display:none}.languageDropdown .nav-item:hover .globalIcon .black,.languageDropdown .nav-item.show .globalIcon .black{display:none}.languageDropdown .nav-item:hover .globalIcon .blue,.languageDropdown .nav-item.show .globalIcon .blue{display:block}.userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name{display:none}@media screen and (max-width: 1199px){.userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name{display:block;padding-left:10px;font-size:18px}.userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name svg{width:26px;height:26px}.userDropdown.common_dropdown.dropdown .dropdown-toggle{border-bottom:0 !important}}.userDropdown.common_dropdown.dropdown .dropdown-toggle:hover{background-color:rgba(0,0,0,0) !important}@media(max-width: 1199px){.brandLogo{display:flex}.brandLogo img{max-width:150px}}@media(max-width: 767px){.brandLogo img{max-width:110px}}@media(max-width: 359px){.brandLogo img{max-width:100px}}.sidebar_backdrop{position:fixed;top:0;left:0;width:100%;height:100vh;z-index:1000;background-color:rgba(0,0,0,.2);transition:all ease-in-out .2s}.image_color_to_white{filter:brightness(0) invert(1)}@media(min-width: 1200px){.nav-link:hover,.nav-link.active,.nav-link:focus{background-color:#2a2e39 !important;color:#fff}}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;;;;AAAiD;;;;;AAAwE;EAA0B;;;;;EAAgN;;;;;AAAyM;;;;AAAgH;EAA0B;;;;;AAAqG;;;;AAAoS;;;;;;;AAAiN;;;;AAAuH;;;;AAA6O;EAA0B;;;;;;;EAAoL;;;;EAA0F;;;;;AAAoF;EAAwB;;;;;EAAwN;;;;;AAAyC;EAA+C;;;;EAAwC;;;;;AAA0D;EAAwB;;;;;EAA0O;;;;;AAA8P;;;;;;;;;;;AAAkL;;;;;AAAuD;EAA0B;;;;;;;;AAAwF;EAAyB;;;;;;;AAAsE;EAA0B;;;;;;AAA+C;EAAyB;;;;;AAA+B;;;;;AAAyC;;;;;AAA8D;EAAyB;;;;;;AAAsE;EAAyB;;;;;;AAAsE;;;;AAAoD;;;;;;;;;;;AAA2L;;;;AAAsJ;EAA0B;;;;;EAAuL;;;;;AAAqD;EAA0B;;;;;;EAA0H;;;;EAAsD;;;;;;;EAA8J;;;;EAA+E;;;;;;;;;;;;;;;EAAyP;;;;;;EAAuF;;;;;EAAsD;;;;;;;AAA8F;EAAyB;;;;;;AAAoD;;;;;AAAiI;EAA0B;;;;;;;AAAwK;;;;;;;;;;;;;AAA8W;EAA0B;;;;;;;AAAiI;EAA0B;;;;;EAAiN;;;;;AAAqN;;;;AAAsH;EAAsC;;;;;;;;AAAkJ;;;;;;AAA6J;EAAsC;;;;;AAA+G;;;;;;;;;AAAoI;;;;AAA0D;EAA0B;;;;;AAAuD;EAAyB;;;;;AAAuD;;;;;;;;;;;AAAmK;;;;;;;;;;;AAAiK;;;;;;;;;AAAuK;;;;;;;;;;;AAAoM;EAAyB;;;;;AAAmF;;;;AAAmF;;;;AAA0F;;;;;;;;AAAkK;EAAsC;;;;;;;AAAuG;;;;;;;AAA+I;EAAyB;;;;;AAA4F;;;;AAAkL;;;;AAA0Q;;;;;;;AAAmM;;;;AAAoH;;;;AAAuO;;;;AAAuJ;EAAsC;;;;;EAAkG;;;;EAA0D;;;;EAAoE;;;;;;AAAmF;;;;;AAAwH;;;;;AAAyH;;;;AAA4E;;;;;AAAkD;EAAqC;;;;;AAA4C;;;;AAA6B;EAA0B;;;;EAA6B;;;;;AAA+C;;;;;AAAyM;;;;;;;;;;AAAwK;EAAyB;;;;;AAA6E;;;;AAAmF;;;;AAAgL;EAA0B;;;;;AAAyE;;;;AAAgE;;;;AAAiD;;;;AAAsH;;;;AAAqH;;;;AAAgF;EAAsC;;;;;;EAAkH;;;;;EAA8F;;;;;AAAoF;;;;AAAwG;EAA0B;;;;EAAwB;;;;;AAAgC;EAAyB;;;;;AAAgC;EAAyB;;;;;AAAgC;;;;;;;;;;;AAAkJ;;;;AAAqD;EAA0B", "debugId": null}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 810, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/Footer.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}.site_footer{background-color:#000}.site_footer_inner{padding:70px 0}@media screen and (max-width: 991px){.site_footer_inner{padding:40px 0}}.site_footer_logo img{width:200px}.site_footer_content p{color:hsla(0,0%,100%,.65);font-size:18px;font-weight:600;line-height:26px;letter-spacing:-.1000000015px;margin-top:20px}@media screen and (max-width: 991px){.site_footer_content p{font-size:16px}}@media screen and (max-width: 767px){.site_footer_links{margin-top:20px}}.site_footer_links h4{color:#c5c5d5;margin-bottom:1.25rem;font-size:1.65rem;line-height:35px;font-weight:600}@media screen and (max-width: 991px){.site_footer_links h4{font-size:18px}}.site_footer_links ul li a{font-size:20px;font-weight:600;line-height:24.5px;letter-spacing:-.1000000015px;color:#fff;transition:all ease-in-out .3s;padding-bottom:10px}@media screen and (max-width: 991px){.site_footer_links ul li a{font-size:16px}}.site_footer_links ul li a:hover,.site_footer_links ul li a.active{color:#00adef}.site_footer_copyright{padding:1.25rem 0;border-top:1px solid #fff}.site_footer_copyright p{text-align:center;font-size:18px;font-weight:600;line-height:26px;letter-spacing:-.1000000015px}@media screen and (max-width: 991px){.site_footer_copyright p{font-size:16px}}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;AAAmC;;;;AAAkC;EAAqC;;;;;AAAmC;;;;AAAkC;;;;;;;;;AAA+I;EAAqC;;;;;AAAuC;EAAqC;;;;;AAAoC;;;;;;;;AAA6G;EAAqC;;;;;AAAsC;;;;;;;;;;AAAyK;EAAqC;;;;;AAA2C;;;;AAAiF;;;;;AAAmE;;;;;;;;AAAyH;EAAqC", "debugId": null}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 908, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/css/account/AccountSidebar.scss.css"], "sourcesContent": [":root{--font-gilroy: \"<PERSON><PERSON>\", sans-serif}.Account_sidebar{position:sticky;top:88px;left:0;background-color:#031940;padding:1.875rem 1.5rem 1rem;height:calc(100vh - 88px);overflow-y:auto;width:355px;transition:all ease-in-out .3s;z-index:999}@media(max-width: 1199px){.Account_sidebar{left:-325px;width:325px;height:100vh;position:fixed;top:0;padding:6rem 1.5rem 1.875rem;z-index:9999;background-color:rgba(3,25,64,.9);backdrop-filter:blur(5px)}}@media(max-width: 767px){.Account_sidebar{left:-100%;width:100%}}@media(max-width: 1199px){.Account_sidebar.opensidebar{left:0}.Account_sidebar_head{position:absolute;left:50%;transform:translateX(-50%);top:22px;width:100%;text-align:center}.Account_sidebar_head::after{content:\"\";position:absolute;right:0;bottom:-20px;width:100%;height:1px;background-color:#666}.Account_sidebar_head .headLogo img{width:150px}}.Account_sidebar ul{padding:0;margin:0;list-style:none}.Account_sidebar ul li{margin-bottom:10px}.Account_sidebar ul li:last-child{margin-bottom:0}.Account_sidebar ul li a{color:#fff;font-size:1.125rem;font-weight:600;line-height:24.5px;letter-spacing:-.1000000015px;text-align:left;padding:15px 1rem;border-left:3px solid rgba(0,0,0,0);position:relative;z-index:1;display:flex;align-items:center}.Account_sidebar ul li a::after{content:\"\";background:linear-gradient(90deg, rgba(4, 73, 140, 0.5) 0%, rgba(4, 73, 140, 0) 100%);position:absolute;bottom:0;left:0;width:0;height:100%;z-index:-1;transition:all ease-in-out .3s}.Account_sidebar ul li a span{margin-right:15px}.Account_sidebar ul li a span svg{width:20px}.Account_sidebar ul li a:hover,.Account_sidebar ul li a.active{border-color:#00adef}.Account_sidebar ul li a:hover::after,.Account_sidebar ul li a.active::after{width:100%}.Account_sidebar_bottom_link{border-top:1px solid #04498c;padding-top:1.25rem;margin-top:1.25rem}.Account_sidebar_bottom_link ul li a svg{margin-left:1rem;transition:all ease-in-out .3s}.Account_sidebar_bottom_link ul li a:hover svg{margin-left:1.5rem}.Account_sidebar .filter_toggle{display:none}@media(max-width: 1199px){.Account_sidebar .filter_toggle{position:absolute;top:0;display:block;right:20px;left:auto}}.Account_sidebar::-webkit-scrollbar{width:5px;height:4px;border-radius:1rem}.Account_sidebar::-webkit-scrollbar-thumb{background-color:#0557a3;border-radius:1rem}.filter_toggle{display:none}@media(max-width: 1199px){.filter_toggle{position:absolute;top:51px;left:20px;display:block}}.filter_toggle_btn{background-color:rgba(0,0,0,0);margin-left:1.25rem;padding:0;position:relative;width:26px;height:22px;border:0}.filter_toggle_btn:focus{box-shadow:none;border:0;outline:0}@media(max-width: 1199px){.filter_toggle_btn{margin-left:0}}@media(max-width: 767px){.filter_toggle_btn{margin-left:0}}.filter_toggle_btn::after{content:\"\";position:absolute;bottom:0;left:0;width:26px;background-color:#fff;height:2px;transition:all ease-in-out .3s}.filter_toggle_btn::before{content:\"\";position:absolute;top:0;left:0;width:26px;background-color:#fff;height:2px;transition:all ease-in-out .3s}.filter_toggle_btn span{background-image:none;height:2px;background-color:#00adef;width:20px;transition:all ease-in-out .3s;display:flex}.filter_toggle_btn.active::after{transform:rotate(45deg) translate(-6px, -6px)}.filter_toggle_btn.active::before{transform:rotate(-45deg) translate(-8px, 8px)}.filter_toggle_btn.active span{opacity:0}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;;;;;;;;;;AAAwM;EAA0B;;;;;;;;;;;;;AAAkL;EAAyB;;;;;;AAAwC;EAA0B;;;;EAAoC;;;;;;;;;EAAkH;;;;;;;;;;EAA2H;;;;;AAAiD;;;;;;AAAuD;;;;AAA0C;;;;AAAkD;;;;;;;;;;;;;;;AAA0P;;;;;;;;;;;;AAAiO;;;;AAAgD;;;;AAA6C;;;;AAAoF;;;;AAAwF;;;;;;AAAiG;;;;;AAAyF;;;;AAAkE;;;;AAA6C;EAA0B;;;;;;;;;AAA4F;;;;;;AAA4E;;;;;AAAsF;;;;AAA4B;EAA0B;;;;;;;;AAAmE;;;;;;;;;;AAAkI;;;;;;AAA4D;EAA0B;;;;;AAAkC;EAAyB;;;;;AAAkC;;;;;;;;;;;AAAkJ;;;;;;;;;;;AAAgJ;;;;;;;;;AAAyI;;;;AAA+E;;;;AAAgF", "debugId": null}}, {"offset": {"line": 1155, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1158, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/css/account/AccountLayout.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}.Account_layout_main{display:flex}.Account_layout_rightaside{width:calc(100% - 355px);padding:2.813rem 1.875rem;color:#fff;position:relative}@media(min-width: 992px){.Account_layout_rightaside .mb-lg-4{margin-bottom:1.875rem !important}}@media(max-width: 1199px){.Account_layout_rightaside{width:100%;padding:2.813rem 1rem}}.sidebar_heading{width:100%;margin-bottom:26px}@media screen and (max-width: 1199px){.sidebar_heading{padding-left:45px}}.sidebar_heading_top{display:flex;justify-content:space-between;align-items:center;gap:20px}.sidebar_heading h2{font-size:48px;font-weight:400;font-family:\"Gilroy-Bold\",sans-serif;display:inline-flex}@media(max-width: 1199px){.sidebar_heading h2{font-size:1.688rem}}@media(max-width: 767px){.sidebar_heading h2{font-size:1.5rem}}.sidebar_heading_icon button{background-color:rgba(0,0,0,0);border:0;color:#00adef;font-size:1.15rem;padding:0;font-weight:600;transition:all ease-in-out .3s}@media(max-width: 991px){.sidebar_heading_icon button{font-size:14px;display:flex;align-items:center;justify-content:flex-end}}.sidebar_heading_icon button .me-2{margin-right:10px !important}.sidebar_heading_icon button .ms-2{margin-left:10px !important}.sidebar_heading_icon button svg{margin-left:10px;transition:all ease-in-out .3s;width:16px}@media(max-width: 991px){.sidebar_heading_icon button svg{width:14px}}@media(max-width: 767px){.sidebar_heading_icon button svg{width:10px;margin-left:0px}}.sidebar_heading_icon button svg path{fill:#00adef}.sidebar_heading_icon button:hover{color:#fea500}.sidebar_heading_icon button:hover svg{margin-left:12px}@media(max-width: 991px){.sidebar_heading_icon button:hover svg{margin-left:10px}}.sidebar_heading_icon button:hover svg path{fill:#fea500}.common_blackcard,.common_whitecard{background-color:#191c23;padding:10px;border-radius:15px;border:1px solid #04498c;width:100%;height:100%}.common_blackcard_innerheader,.common_whitecard_innerheader{background-color:#031940;padding:.625rem 1.25rem;border-radius:15px;display:flex;align-items:center;justify-content:space-between;font-family:\"Gilroy-Semibold\",sans-serif;font-weight:400}@media(max-width: 991px){.common_blackcard_innerheader,.common_whitecard_innerheader{padding:.625rem .625rem}}.common_blackcard_innerheader p,.common_whitecard_innerheader p{color:#fff}@media(max-width: 991px){.common_blackcard_innerheader p,.common_whitecard_innerheader p{font-size:14px}}@media(max-width: 767px){.common_blackcard_innerheader p,.common_whitecard_innerheader p{font-size:14px}}.common_blackcard_innerheader_content,.common_whitecard_innerheader_content{padding-right:30px;font-size:18px;font-weight:400}.common_blackcard_innerheader_content h6,.common_whitecard_innerheader_content h6{font-weight:600}@media(max-width: 991px){.common_blackcard_innerheader_content,.common_whitecard_innerheader_content{padding-right:15px}.common_blackcard_innerheader_content h6,.common_whitecard_innerheader_content h6{font-size:18px !important}}@media(max-width: 767px){.common_blackcard_innerheader_content,.common_whitecard_innerheader_content{padding-right:5px}}@media(max-width: 400px){.common_blackcard_innerheader_content h6,.common_whitecard_innerheader_content h6{font-size:14px !important}}.common_blackcard_innerheader_icon button,.common_whitecard_innerheader_icon button{background-color:rgba(0,0,0,0);border:0;color:#00adef;font-size:18px;padding:0;font-weight:600;font-family:\"Gilroy-Semibold\",sans-serif;transition:all ease-in-out .3s;white-space:nowrap}@media(max-width: 991px){.common_blackcard_innerheader_icon button,.common_whitecard_innerheader_icon button{font-size:14px;display:flex;align-items:center;justify-content:flex-end}}.common_blackcard_innerheader_icon button .me-2,.common_whitecard_innerheader_icon button .me-2{margin-right:10px !important}.common_blackcard_innerheader_icon button .ms-2,.common_whitecard_innerheader_icon button .ms-2{margin-left:10px !important}.common_blackcard_innerheader_icon button .link-icon,.common_whitecard_innerheader_icon button .link-icon{flex-shrink:0;height:auto}.common_blackcard_innerheader_icon button svg,.common_whitecard_innerheader_icon button svg{transition:all ease-in-out .3s;width:16px}@media(max-width: 991px){.common_blackcard_innerheader_icon button svg,.common_whitecard_innerheader_icon button svg{width:14px}}@media(max-width: 767px){.common_blackcard_innerheader_icon button svg,.common_whitecard_innerheader_icon button svg{width:10px;margin-left:0px}}.common_blackcard_innerheader_icon button svg path,.common_whitecard_innerheader_icon button svg path{fill:#00adef}.common_blackcard_innerheader_icon button:hover,.common_whitecard_innerheader_icon button:hover{color:#fea500}.common_blackcard_innerheader_icon button:hover svg,.common_whitecard_innerheader_icon button:hover svg{margin-left:12px}@media(max-width: 991px){.common_blackcard_innerheader_icon button:hover svg,.common_whitecard_innerheader_icon button:hover svg{margin-left:10px}}.common_blackcard_innerheader_icon button:hover svg path,.common_whitecard_innerheader_icon button:hover svg path{fill:#fea500}.common_blackcard_innerheader_tradeacct,.common_whitecard_innerheader_tradeacct{min-width:200px;width:200px}.common_blackcard_innerheader_tradeacct h6,.common_whitecard_innerheader_tradeacct h6{background-color:#04498c;padding:8px 15px;border-top-left-radius:10px;border-top-right-radius:10px;color:#fff;font-size:1.25rem;line-height:normal}@media(max-width: 991px){.common_blackcard_innerheader_tradeacct h6,.common_whitecard_innerheader_tradeacct h6{font-size:16px}}.common_blackcard_innerheader_tradeacct p,.common_whitecard_innerheader_tradeacct p{background-color:#fff;padding:8px 15px;border-bottom-left-radius:10px;border-bottom-right-radius:10px;color:#000;font-size:1.25rem;font-weight:600;margin:0;line-height:normal}@media(max-width: 991px){.common_blackcard_innerheader_tradeacct p,.common_whitecard_innerheader_tradeacct p{font-size:16px}}.common_blackcard_innerbody,.common_whitecard_innerbody{padding:1.25rem .625rem}.connetionTable .common_blackcard_innerbody,.connetionTable .common_whitecard_innerbody,.removePadding .common_blackcard_innerbody,.removePadding .common_whitecard_innerbody{padding:0}@media(max-width: 991px){.account_card.pullcontent .common_blackcard_innerheader_icon,.account_card.pullcontent .common_whitecard_innerheader_icon{margin-top:10px}}.common_whitecard{background-color:#fff !important}.common_whitecard_innerbody{padding:.8rem .3rem 0px .3rem}.account_card .label{color:#fff;font-size:1.125rem;font-weight:600}.account_card_list_btns{display:flex;justify-content:end;align-items:center;gap:10px}.account_card_list_btns .btn-style{min-height:40px;font-size:16px !important}.account_card_list_form{width:40%}.account_card_list_form .row{width:100% !important}.account_card_list ul li{display:flex;justify-content:space-between;margin-bottom:1.25rem;color:#fff;font-size:1.125rem;font-weight:600}@media(max-width: 991px){.account_card_list ul li{font-size:15px;margin-bottom:1rem}}.account_card_list ul li:last-child{margin-bottom:0}.account_card_list ul li span{color:#fff}.account_card_list p{color:#c5c5c5}.account_card_redeem .form-control{width:calc(100% - 136px);margin-right:10px}.account_card_redeem ::placeholder{color:#fff;opacity:1}.account_card_redeem .btn-style{font-size:16px;font-weight:600;line-height:26px;min-width:150px;padding:8px 15px;min-height:56px}@media(max-width: 991px){.account_card_redeem .btn-style{font-size:14px;line-height:24px;min-width:130px;padding:8px 15px;min-height:52px}}.account_card_redeem .error-messages .success{color:#32cd33}.account_card_redeem .error-messages .invalid{color:#ff696a}.account_card_checkup_verify{width:calc(100% - 150px);padding-right:30px}@media(max-width: 991px){.account_card_checkup_verify{width:calc(100% - 100px);padding-right:0}}.account_card_checkup_chart{width:150px;text-align:center}@media(max-width: 991px){.account_card_checkup_chart{width:100px}.account_card_checkup_chart .CircularProgressbar_text h6{font-size:14px}}.account_card_subscription_list{display:flex;flex-wrap:wrap}.account_card_subscription_list li{width:25%}@media(max-width: 767px){.account_card_subscription_list li{width:50%;padding:5px 0}.account_card_subscription_list li:nth-child(2n){text-align:right}}.account_card_subscription_list li p{color:#fff;font-weight:600;margin-top:.5rem}@media(max-width: 991px){.account_card_subscription_list li p{font-size:14px;line-height:20px}}@media(max-width: 767px){.account_card_subscription_list li p{margin-top:0}}@media(max-width: 991px){.account_card_subscription_list li h6{font-size:15px}}.account_card_table .simple_table_imgIcon{min-width:20px}.account_card_table .tableless{border:0}.account_card_table .tableless::before{display:none}.account_card_table .tableless .common_table tr th,.account_card_table .tableless .common_table tr td{border:0}.account_card_table .tableless .common_table tr td{border-bottom:1px solid #666;color:#fff;font-weight:600 !important}.account_card_table .tableless .common_table thead tr{border-radius:15px}.account_card_table .tableless .common_table thead tr th{background-color:#031940;padding:10px 15px;font-size:1rem}.account_card_table .tableless .common_table thead tr th:first-child{border-top-left-radius:15px;border-bottom-left-radius:15px}.account_card_table .tableless .common_table thead tr th:last-child{border-top-right-radius:15px;border-bottom-right-radius:15px}.account_card .add_phone_number,.account_card .add_number,.account_card .blue_text_btn{background-color:rgba(0,0,0,0);border:0;color:#00adef;font-size:1.25rem;font-weight:600;line-height:24.5px;letter-spacing:-.1000000015px;transition:all ease-in-out .3s;padding:0}@media(max-width: 991px){.account_card .add_phone_number,.account_card .add_number,.account_card .blue_text_btn{font-size:1rem}}.account_card .add_phone_number:hover,.account_card .add_number:hover,.account_card .blue_text_btn:hover{color:#fea500}.account_card .add_number svg,.account_card .add_number img,.account_card .blue_text_btn svg,.account_card .blue_text_btn img{width:16px;height:16px;margin-right:10px}.account_card .add_number svg path,.account_card .add_number img path,.account_card .blue_text_btn svg path,.account_card .blue_text_btn img path{fill:#00adef}.account_card .add_number:hover svg path,.account_card .blue_text_btn:hover svg path{fill:#fea500}.CircularProgressbar_text h6{font-size:14px !important}.account-custom-select{position:relative}.account-custom-select .header{min-height:56px;box-shadow:none;outline:none;width:100%;padding:.5rem 1.25rem;border-radius:1rem;background-color:hsla(0,0%,100%,.**********);color:#fff;font-size:1rem;display:flex;justify-content:space-between;align-items:center;cursor:pointer}.account-custom-select .header span{width:100% !important}.account-custom-select .body{width:100%;padding:.5rem 1.25rem;position:absolute;top:65px;border-radius:1rem;border:1px solid hsla(0,0%,100%,.**********);background-color:#191c23;max-height:250px;overflow-y:scroll;z-index:999}.account-custom-select .body .search{background-color:hsla(0,0%,100%,.**********);margin:.5rem 0;padding:.5rem 1.25rem;width:100%;border-radius:1rem;display:flex;gap:10px;align-items:center}.account-custom-select .body .search input{background-color:rgba(0,0,0,0);width:100%;color:#fff}.account-custom-select .body .search input:focus-visible{outline:none !important}.account-custom-select .body ul li{cursor:pointer;padding:10px;border-radius:8px;border-bottom:0 !important;margin-bottom:0 !important}.account-custom-select .body ul li:hover{background-color:hsla(0,0%,100%,.**********)}.account-custom-select.simple .header{border:1px solid rgba(0,0,0,.2);background-color:#f2f2f2;min-height:34px;padding:7px 10px;border-radius:10px}.account-custom-select.simple .header span{color:#000 !important;font-size:14px;font-weight:600}.account-custom-select.simple .body{width:396px;padding:10px 12px;top:45px;right:0;border-radius:.94rem;border:1px solid rgba(6,6,6,.**********);background-color:#fff;max-height:200px}@media only screen and (max-width: 500px){.account-custom-select.simple .body{width:auto}}.account-custom-select.simple .body.align-left{left:0;right:auto}.account-custom-select.simple .body.align-right{right:0;left:auto}.account-custom-select.simple .body::-webkit-scrollbar{display:block}.account-custom-select.simple .body .option-heading{display:flex;align-items:center;gap:5px;margin:6px 0px 2px 0px}.account-custom-select.simple .body .option-heading p{color:#000;font-size:14px;font-weight:700}.account-custom-select.simple .body ul li{font-size:14px;font-weight:600;padding:4px 0px;border-radius:0 !important;border-bottom:1px solid rgba(0,0,0,.2)}.account-custom-select.simple .body ul li:hover{background-color:rgba(211,211,211,.166)}.width-autofit{width:fit-content !important}.text_00ADEF{color:#00adef !important}input::-webkit-outer-spin-button,input::-webkit-inner-spin-button{-webkit-appearance:none;margin:0}.switch{position:relative;display:inline-block;width:50px;height:28px}.switch input{opacity:0;width:0;height:0}.slider{position:absolute;cursor:pointer;top:0;left:0;right:0;bottom:0;background-color:#fff;transition:.4s;border-radius:34px}.slider:before{position:absolute;content:\"\";height:22px;width:22px;left:3px;bottom:3px;background-color:#9c9a9f;transition:.4s;border-radius:50%}input:checked+.slider{background-color:#0099d1}input:checked+.slider:before{transform:translateX(22px);background-color:#fff}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;AAAkC;;;;;;;AAA2G;EAAyB;;;;;AAAuE;EAA0B;;;;;;AAA6D;;;;;AAA+C;EAAsC;;;;;AAAoC;;;;;;;AAA4F;;;;;;;AAA4G;EAA0B;;;;;AAAwC;EAAyB;;;;;AAAsC;;;;;;;;;;AAA8J;EAAyB;;;;;;;;AAAsG;;;;AAAgE;;;;AAA+D;;;;;;AAA4F;EAAyB;;;;;AAA6C;EAAyB;;;;;;AAA6D;;;;AAAmD;;;;AAAiD;;;;AAAwD;EAAyB;;;;;AAAyD;;;;AAAyD;;;;;;;;;AAA6I;;;;;;;;;;;AAAuP;EAAyB;;;;;AAAqF;;;;AAA2E;EAAyB;;;;;AAAgF;EAAyB;;;;;AAAgF;;;;;;AAA8H;;;;AAAkG;EAAyB;;;;EAA+F;;;;;AAA6G;EAAyB;;;;;AAA+F;EAAyB;;;;;AAA6G;;;;;;;;;;;;AAA8Q;EAAyB;;;;;;;;AAA6J;;;;AAA6H;;;;AAA4H;;;;;AAAoI;;;;;AAAsI;EAAyB;;;;;AAAwG;EAAyB;;;;;;AAAwH;;;;AAAmH;;;;AAA8G;;;;AAAyH;EAAyB;;;;;AAA0H;;;;AAA+H;;;;;AAA4G;;;;;;;;;;AAAyO;EAAyB;;;;;AAAsG;;;;;;;;;;;;AAAmQ;EAAyB;;;;;AAAoG;;;;AAAgF;;;;AAAwL;EAAyB;;;;;AAA2I;;;;AAAmD;;;;AAA0D;;;;;;AAAmE;;;;;;;AAAqF;;;;;AAA6E;;;;AAAkC;;;;AAAmD;;;;;;;;;AAAwI;EAAyB;;;;;;AAA4D;;;;AAAoD;;;;AAAyC;;;;AAAmC;;;;;AAA8E;;;;;AAAwD;;;;;;;;;AAAiI;EAAyB;;;;;;;;;AAAkH;;;;AAA4D;;;;AAA4D;;;;;AAAyE;EAAyB;;;;;;AAAuE;;;;;AAA0D;EAAyB;;;;EAAwC;;;;;AAAyE;;;;;AAA4D;;;;AAA6C;EAAyB;;;;;EAA2D;;;;;AAAmE;;;;;;AAAiF;EAAyB;;;;;;AAAsE;EAAyB;;;;;AAAmD;EAAyB;;;;;AAAsD;;;;AAAyD;;;;AAAwC;;;;AAAoD;;;;AAA+G;;;;;;AAAsH;;;;AAAyE;;;;;;AAAmH;;;;;AAAgI;;;;;AAAiI;;;;;;;;;;;;AAAyQ;EAAyB;;;;;AAAuG;;;;AAAuH;;;;;;AAAuK;;;;AAA+J;;;;AAAkG;;;;AAAuD;;;;AAAyC;;;;;;;;;;;;;;;;AAAoR;;;;AAA0D;;;;;;;;;;;;;AAAiO;;;;;;;;;;;AAA8L;;;;;;AAAgG;;;;AAAiF;;;;;;;;AAAuI;;;;AAAsF;;;;;;;;AAAmJ;;;;;;AAAgG;;;;;;;;;;;AAAwL;EAA0C;;;;;AAAgD;;;;;AAAiE;;;;;AAAkE;;;;AAAqE;;;;;;;AAAmH;;;;;;AAAgG;;;;;;;;AAA2J;;;;AAAwF;;;;AAA4C;;;;AAAsC;;;;;AAAmG;;;;;;;AAAsE;;;;;;AAAyC;;;;;;;;;;;;AAA+H;;;;;;;;;;;;AAAiJ;;;;AAA+C", "debugId": null}}, {"offset": {"line": 1943, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1946, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/css/account/Security.scss.css"], "sourcesContent": [":root{--font-gilroy: \"<PERSON><PERSON>\", sans-serif}.security_sec .account_card_list ul li{justify-content:flex-start}.security_sec .account_card_list ul li span{padding-right:15px}.security_sec .account_card_list p{color:#c5c5d5}.security_sec .table_heading{border:1px solid #666;border-radius:15px;padding:10px 1.25rem}.modal-content{background-color:#031940 !important;border:1px solid #04498c;border-radius:15px;color:#fff}.account_card_list_btns{display:flex;justify-content:end;align-items:center;gap:10px}@media(max-width: 360px){.account_card_list_btns{display:flex;justify-content:center;align-items:center;gap:5px}.confirm-modal-btn{justify-content:center !important;gap:8px !important;flex-wrap:wrap}}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;AAAkE;;;;AAA+D;;;;AAAiD;;;;;;AAA2F;;;;;;;AAA0G;;;;;;;AAAqF;EAAyB;;;;;;;EAAuF", "debugId": null}}, {"offset": {"line": 1996, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1999, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/css/account/AccountDetails.scss.css"], "sourcesContent": [":root{--font-gilroy: \"<PERSON><PERSON>\", sans-serif}.account_details .account_card_list .form-control{color:#fff !important}.account_details .account_card_list ul li{justify-content:flex-start !important}@media screen and (max-width: 767px){.account_details .account_card_list ul li{justify-content:space-between}}.account_details .account_card_list ul li span{padding-right:15px}.add_plusicon button svg{width:16px;height:16px;margin-right:10px}.add_plusicon button svg path{fill:#00adef}.account_setup_phone_number label{cursor:pointer}.account_setup_phone_number label p{font-weight:300}.new-address-section ul li{border-bottom:1px solid #666;padding-bottom:1.25rem;align-items:center}.new-address-section ul li:last-child{padding-bottom:0 !important;border-bottom:none !important}.new-address-section .show-address-details{display:flex;flex-direction:column;gap:4px}.new-address-section .show-address-details .name{font-size:1.125rem !important;font-weight:600 !important;color:#fff;margin-top:0 !important}.new-address-section .show-address-details .address,.new-address-section .show-address-details .city{font-size:1rem;font-weight:400;color:#fff;margin-top:0 !important}.new-address-section .show-address-details .remove-address-confirmation p{font-size:1.125rem !important;font-weight:600 !important;color:#fff}.new-address-section .show-address-details .remove-address-confirmation .btns{margin-top:10px;display:flex;align-items:center;gap:10px}.new-address-section .show-address-details .remove-address-confirmation .btns .btn-style{min-height:40px}.new-address-section .btns{display:flex;align-items:center;gap:10px}.new-address-section .btns button{display:flex;align-items:center;gap:10px}.new-address-section .btns button svg{width:16px;height:16px}.new-address-section .btns button svg path{fill:#00adef}.new-address-section .btns button span{color:#00adef;font-size:18px;padding:0;font-weight:600;font-family:Gilroy-Semibold,sans-serif;transition:all .3s ease-in-out;white-space:nowrap}.new-address-section .btns button:hover span{color:#fea500}.new-address-section .btns button:hover svg path{fill:#fea500}.border-bottom-none{border-bottom:0 !important}.account_header_main{display:flex;flex-direction:column;gap:8px}.account_header_main h6{margin:0;font-size:1.125rem;font-weight:600;color:#fff;line-height:1.2}@media(min-width: 768px){.account_header_main{flex-direction:row;align-items:center;justify-content:space-between;gap:16px}.account_header_main h6{flex:1}}.account_status_indicator{display:flex;align-items:center}@media(max-width: 767px){.account_status_indicator{margin-left:0}}.status_indicator{display:flex;align-items:center;gap:6px;padding:4px 8px;border-radius:6px;background-color:hsla(0,0%,100%,.05);border:1px solid hsla(0,0%,100%,.1);transition:all .2s ease-in-out}.status_indicator span{font-weight:500;font-size:13px;color:hsla(0,0%,100%,.9);white-space:nowrap;line-height:1}.status_indicator svg,.status_indicator img{width:16px;height:16px;flex-shrink:0}.status_indicator.status-loading{background-color:rgba(0,173,239,.1);border-color:rgba(0,173,239,.2)}.status_indicator.status-loading span{color:#00adef}.status_indicator.status-success{background-color:rgba(40,167,69,.1);border-color:rgba(40,167,69,.2)}.status_indicator.status-success span{color:#28a745}.status_indicator.status-error{background-color:rgba(220,53,69,.1);border-color:rgba(220,53,69,.2)}.status_indicator.status-error span{color:#dc3545}.status_indicator.status-default{background-color:rgba(108,117,125,.1);border-color:rgba(108,117,125,.2)}.status_indicator.status-default span{color:#fff}@media(max-width: 767px){.status_indicator{padding:3px 6px}.status_indicator span{font-size:12px}.status_indicator svg,.status_indicator img{width:14px;height:14px}}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;AAAwE;;;;AAAgF;EAAqC;;;;;AAAyE;;;;AAAkE;;;;;;AAAkE;;;;AAA2C;;;;AAAiD;;;;AAAoD;;;;;;AAAkG;;;;;AAAgG;;;;;;AAAsF;;;;;;;AAA6I;;;;;;;AAAuK;;;;;;AAA8I;;;;;;;AAAuI;;;;AAAyG;;;;;;AAAoE;;;;;;AAA2E;;;;;AAA6D;;;;AAAwD;;;;;;;;;;AAAuL;;;;AAA2D;;;;AAA8D;;;;AAA+C;;;;;;AAAgE;;;;;;;;AAA+F;EAAyB;;;;;;;EAAkG;;;;;AAAgC;;;;;AAA0D;EAAyB;;;;;AAAyC;;;;;;;;;;;AAAoM;;;;;;;;AAAgH;;;;;;AAAiF;;;;;AAAqG;;;;AAAoD;;;;;AAAqG;;;;AAAoD;;;;;AAAmG;;;;AAAkD;;;;;AAAyG;;;;AAAiD;EAAyB;;;;EAAkC;;;;EAAsC", "debugId": null}}, {"offset": {"line": 2243, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2246, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/CommonTooltip.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}.tooltip-container{position:relative;display:inline-block;cursor:pointer}.tooltip-container img{max-width:20px;min-width:20px;min-height:20px;max-height:20px}.tooltip-wrapper{flex-shrink:0;cursor:pointer}.tooltip-box{position:absolute;padding:5px 10px;z-index:1000;min-width:300px;width:300px;background-color:rgba(2.55,45.9,89.25,.699);color:#fff;text-align:left;padding:10px 15px;border-radius:5px;backdrop-filter:blur(6px);pointer-events:auto !important}.tooltip-box p,.tooltip-box a{font-size:.875rem !important;font-weight:300;line-height:20px}@media screen and (max-width: 991px){.tooltip-box p,.tooltip-box a{font-size:14px;line-height:18px}.tooltip-box{min-width:200px;width:200px}}.tooltip-top-left{top:0;right:0;transform:translateY(-100%)}.tooltip-top-right{top:0;left:0;transform:translateY(-100%)}.tooltip-bottom-left{bottom:0;right:0;transform:translateY(100%)}.tooltip-bottom-right{bottom:0;left:0;transform:translateY(100%)}.tooltip-center-bottom{top:25px;left:50%;transform:translateX(-50%)}.tooltip-center-top{bottom:100%;left:50%;transform:translateX(-50%) translateY(-10px)}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;;;AAAyE;;;;;;;AAAqF;;;;;AAA8C;;;;;;;;;;;;;;;AAA6P;;;;;;AAA4F;EAAqC;;;;;EAA8D;;;;;;AAA0C;;;;;;AAA4D;;;;;;AAA4D;;;;;;AAAiE;;;;;;AAAiE;;;;;;AAAoE", "debugId": null}}, {"offset": {"line": 2336, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}