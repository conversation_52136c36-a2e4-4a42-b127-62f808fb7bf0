/* [project]/src/css/common/CommonButton.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.btn-style, .btn-primary {
  min-height: 66px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  border-radius: 10rem;
  padding: .5rem 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  background-color: #00adef;
  border: 0;
  text-transform: capitalize;
  transition: all .3s ease-in-out;
  min-width: 150px;
  color: #fff;
}

.btn-style span, .btn-primary span {
  line-height: 1;
}

@media (width <= 1599px) {
  .btn-style, .btn-primary {
    min-height: 66px;
  }
}

@media (width <= 1199px) {
  .btn-style, .btn-primary {
    min-height: 56px;
    font-size: 1.125rem;
    font-weight: 500;
  }
}

@media (width <= 767px) {
  .btn-style, .btn-primary {
    min-height: 46px;
    font-size: 1rem;
  }
}

.btn-style:hover, .btn-primary:hover {
  background-color: #0099d1;
  color: #fff;
}

.btn-style.transparent, .btn-primary.transparent {
  background-color: #0000;
  border: none;
}

.btn-style.white-btn, .btn-primary.white-btn {
  background: #fff;
  color: #000;
}

.btn-style.white-btn:hover, .btn-primary.white-btn:hover {
  background: #00adef;
  color: #fff;
}

.btn-style.yellow-btn, .btn-primary.yellow-btn {
  background-color: #fea500;
  color: #fff;
}

.btn-style.yellow-btn:hover, .btn-primary.yellow-btn:hover {
  background-color: #c9870d;
  color: #fff;
}

.btn-style.gray-btn, .btn-primary.gray-btn {
  color: #fff;
  background-color: #5e6165 !important;
}

.btn-style.gray-btn:hover, .btn-primary.gray-btn:hover {
  background-color: #708090;
  color: #fff;
}

.btn-style.gradient-btn, .btn-primary.gradient-btn {
  background: linear-gradient(75deg, #00aeef, #1f5aff 50.31%, #da00ff);
  color: #fff;
}

.btn-style.gradient-btn:hover, .btn-primary.gradient-btn:hover {
  background: linear-gradient(75deg, #0043ff, #1f5aff 50.31%, #da00ff);
  color: #fff;
}

.btn-style.green-btn, .btn-primary.green-btn {
  background-color: #32cd33;
  color: #fff;
}

.btn-style.green-btn:hover, .btn-primary.green-btn:hover {
  background-color: #2bb72b;
  color: #fff;
}

.btn-style.red-btn, .btn-primary.red-btn {
  background-color: #ff696a;
  color: #fff;
}

.btn-style.red-btn:hover, .btn-primary.red-btn:hover {
  background-color: #e65f60;
  color: #fff;
}

.btn-style.border-btn, .btn-primary.border-btn {
  background: none;
  color: #fff;
  border: 1px solid #00adef;
}

.btn-style.border-btn:hover, .btn-primary.border-btn:hover {
  background: #00adef;
  color: #fff;
}

.btn-style .onlyIcon, .btn-primary .onlyIcon {
  margin-right: 15px;
  display: inline-flex;
}

.btn-style:disabled, .btn-style.disabled, .btn-primary:disabled, .btn-primary.disabled {
  background: #c5c5d5;
  color: #fff;
  cursor: not-allowed;
  opacity: 1;
}

:disabled, .disabled {
  background-color: #414c60;
  color: #fff;
  cursor: not-allowed;
  opacity: 1;
}

.white20 {
  background-color: #ffffff1f;
  width: 100%;
}


/* [project]/src/css/Home/Switch.scss.css [app-client] (css) */
.form-check-input {
  position: relative;
  cursor: pointer;
  appearance: none;
  transition: background-color .3s;
  width: 72px !important;
  height: 34px !important;
  border-radius: 30px !important;
  background-color: #fff !important;
  border: none !important;
}

.form-check-input:before {
  content: "";
  position: absolute;
  width: 30px;
  height: 30px;
  top: 2px;
  left: 2px;
  background-color: #fff;
  border-radius: 50%;
  transition: transform .3s ease-in-out;
  box-shadow: 0 1px 4px #0000004d;
}

.form-check-input:checked {
  background-color: #00adef !important;
}

.form-check-input:checked:before {
  transform: translateX(38px);
}

.form-check-input:focus {
  box-shadow: none !important;
}


/* [project]/src/css/common/CommonHeading.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.common_heading h2, .common_heading h1 {
  font-size: 5rem;
  line-height: normal;
  color: #fff;
  font-weight: 800;
  font-family: Gilroy, sans-serif;
}

@media (width <= 1269px) {
  .common_heading h2, .common_heading h1 {
    font-size: 3rem !important;
  }
}

@media (width <= 991px) {
  .common_heading h2, .common_heading h1 {
    font-size: 3rem !important;
  }
}

@media (width <= 767px) {
  .common_heading h2, .common_heading h1 {
    font-size: 2.5rem !important;
  }
}


/* [project]/src/css/Home/FaqCard.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.faq_card {
  margin-bottom: 6rem;
}

@media screen and (width <= 767px) {
  .faq_card .common_heading h2 {
    font-size: 24px;
  }
}

.faq_card_accordion {
  margin-top: 60px;
}

@media screen and (width <= 991px) {
  .faq_card_accordion {
    margin-top: 15px;
  }
}

.faq_card_accordion .accordion .accordion-item {
  background-color: #0000;
  border: 0;
  border-bottom: 1px solid #666;
  border-radius: 0;
}

.faq_card_accordion .accordion .accordion-item .accordion-header {
  border: 0;
}

.faq_card_accordion .accordion .accordion-item .accordion-header .accordion-button {
  background-color: #0000;
  border: none;
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  line-height: 36px;
  letter-spacing: -1px;
  text-align: left;
  padding: 40px 0;
  border-radius: 0;
  box-shadow: none;
}

@media screen and (width <= 991px) {
  .faq_card_accordion .accordion .accordion-item .accordion-header .accordion-button {
    padding: 20px 0;
  }
}

@media screen and (width <= 767px) {
  .faq_card_accordion .accordion .accordion-item .accordion-header .accordion-button {
    font-size: 16px;
    line-height: 24px;
  }
}

.faq_card_accordion .accordion .accordion-item .accordion-header .accordion-button:after {
  background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg");
  background-size: 32px;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
}

@media screen and (width <= 991px) {
  .faq_card_accordion .accordion .accordion-item .accordion-header .accordion-button:after {
    background-size: 20px;
    width: 20px;
    height: 20px;
  }
}

.faq_card_accordion .accordion .accordion-item .accordion-header .accordion-button[aria-expanded="true"] {
  transform: none;
}

.faq_card_accordion .accordion .accordion-item .accordion-header .accordion-button[aria-expanded="true"]:after {
  background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-minus.svg");
  height: 4px;
}

@media screen and (width <= 767px) {
  .faq_card_accordion .accordion .accordion-item .accordion-header .accordion-button[aria-expanded="true"]:after {
    height: 2px;
  }
}

.faq_card_accordion .accordion .accordion-collapse .accordion-body {
  padding: 0;
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  line-height: 36px;
  letter-spacing: -1px;
  text-align: left;
  padding-bottom: 30px;
  border: 0;
}

@media screen and (width <= 991px) {
  .faq_card_accordion .accordion .accordion-collapse .accordion-body {
    padding-bottom: 20px;
    font-size: 18px;
    line-height: 30px;
  }
}

@media screen and (width <= 767px) {
  .faq_card_accordion .accordion .accordion-collapse .accordion-body {
    font-size: 15px;
    line-height: 23px;
  }
}

.faq_card_accordion .accordion .accordion-collapse .accordion-body .faq_list {
  list-style-type: disc;
  padding-left: 25px;
  margin-top: 10px;
}

@media screen and (width <= 767px) {
  .faq_card_accordion .accordion .accordion-collapse .accordion-body .faq_list {
    padding-left: 20px;
  }
}

.faq_card_accordion .accordion .accordion-collapse .accordion-body .faq_list li {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

@media screen and (width <= 991px) {
  .faq_card_accordion .accordion .accordion-collapse .accordion-body .faq_list li {
    font-size: 16px;
  }
}

@media screen and (width <= 767px) {
  .faq_card_accordion .accordion .accordion-collapse .accordion-body .faq_list li {
    font-size: 14px;
    margin-bottom: 5px;
  }
}

.faq_card_accordion .accordion .accordion-collapse .accordion-body .faq_list li strong {
  font-weight: 600;
  color: #fff;
}


/* [project]/src/css/common/Header.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

header {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 9998;
}

.home-page .siteHeader {
  border-bottom: 0;
  background-color: #000 !important;
}

@media (width >= 1200px) {
  .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle.show, .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle:hover {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle.show:after, .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle:hover:after {
    transform: rotate(180deg);
  }
}

.home-page .siteHeader .navMenu .common_dropdown.dropdown.show .dropdown-toggle:after {
  transform: rotate(180deg);
}

@media (width >= 1200px) {
  .home-page .siteHeader .navMenu .common_dropdown .dropdown-menu {
    background-color: #1e222d !important;
  }
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item:hover, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.active, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item:focus {
  background-color: #2a2e39 !important;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon {
  font-weight: 700;
  color: #00b9ff;
  transition: none;
  background: linear-gradient(to right, #2a2e39, #1e222d) !important;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon svg path {
  fill: #00b9ff;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
  background: #2a2e39 !important;
}

@media (width <= 1199px) {
  .home-page .siteHeader .navbar-collapse .nav-link.white_stroke_icon {
    font-weight: 700;
    color: #00aeef;
    transition: none;
    background: linear-gradient(to right, #000, #2d2d2d) !important;
  }

  .home-page .siteHeader .navbar-collapse .nav-link.white_stroke_icon svg path {
    fill: #00b9ff;
  }

  .home-page .siteHeader .navbar-collapse {
    background-color: #000000e6 !important;
  }
}

@media (width >= 1200px) {
  .home-page .siteHeader .navbar-collapse .nav-link:hover, .home-page .siteHeader .navbar-collapse .nav-link.active, .home-page .siteHeader .navbar-collapse .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .languageDropdown {
    width: 64px;
  }
}

@media (width >= 1200px) and (width <= 1199px) {
  .home-page .languageDropdown {
    width: 100%;
  }

  .home-page .languageDropdown .common_dropdown {
    width: 100%;
  }
}

@media (width >= 1200px) {
  .home-page .languageDropdown .common_dropdown .nav-link:hover, .home-page .languageDropdown .common_dropdown .nav-link.active, .home-page .languageDropdown .common_dropdown .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .home-page .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
    background: #2a2e39 !important;
  }
}

.siteHeader {
  height: 80px;
  padding: 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  background-color: #031940;
  border-bottom: 1px solid #064197;
}

.siteHeader .btn-style {
  min-height: 56px;
  min-width: 169px;
}

@media (width <= 1199px) {
  .siteHeader .btn-style {
    min-height: 40px;
    min-width: 120px;
    padding: 8px 1rem;
    font-size: 14px;
  }
}

@media (width <= 575px) {
  .siteHeader .btn-style {
    min-height: 34px;
    min-width: 80px;
    font-size: 14px;
  }
}

@media (width <= 1199px) {
  .siteHeader {
    z-index: 9999;
    backdrop-filter: none;
  }
}

@media (width <= 767px) {
  .siteHeader {
    padding: .625rem 0;
  }
}

.siteHeader .navbar {
  padding: 0;
  width: 100%;
}

.siteHeader .navbar .brandLogo img {
  max-width: 190px;
  width: 100%;
}

@media (width <= 767px) {
  .siteHeader .navbar .brandLogo img {
    max-width: 150px;
    margin-right: 0;
  }
}

@media (width <= 360px) {
  .siteHeader .navbar .brandLogo img {
    max-width: 120px;
    margin-right: 0;
  }
}

.siteHeader .navbar-collapse {
  height: auto !important;
}

.siteHeader .navbar-collapse .nav-link {
  font-size: 1.25rem;
  font-weight: 400;
  background-color: #0000;
  display: flex;
  align-items: center;
  white-space: nowrap;
  padding: .5rem 1.5rem;
  color: #fff;
}

.siteHeader .navbar-collapse .nav-link:hover, .siteHeader .navbar-collapse .nav-link.active, .siteHeader .navbar-collapse .nav-link:focus {
  color: #00adef;
}

@media (width >= 1200px) {
  .siteHeader .navbar-collapse .nav-link:hover, .siteHeader .navbar-collapse .nav-link.active, .siteHeader .navbar-collapse .nav-link:focus {
    color: #fff;
    background-color: #283f67 !important;
  }

  .siteHeader .navbar-collapse .nav-link {
    margin: 0 3px;
  }
}

@media (width <= 1199px) {
  .siteHeader .navbar-collapse .nav-link {
    padding: 1.25rem 0;
    border-bottom: 1px solid #fff3;
    font-size: 1.125rem;
  }

  .siteHeader .navbar-collapse .nav-link img {
    width: 22px;
  }

  .siteHeader .navbar-collapse .nav-link.white_stroke_icon {
    font-weight: 700;
    background: linear-gradient(to right, #031940, #283f67);
    color: #00aeef;
    transition: none;
  }

  .siteHeader .navbar-collapse .nav-link.white_stroke_icon svg path {
    fill: #00b9ff;
  }

  .siteHeader .navbar-collapse {
    position: fixed;
    left: -350px;
    top: 0;
    background-color: #031940e6;
    backdrop-filter: blur(5px);
    width: 350px;
    padding: 1.25rem 1rem;
    display: block;
    transition: all .2s ease-in-out;
    z-index: 9999;
    padding: 0;
    height: 100vh !important;
  }

  .siteHeader .navbar-collapse a {
    display: flex;
    justify-content: flex-start;
    text-align: left;
  }

  .siteHeader .navbar-collapse.show {
    left: 0;
    height: 100vh;
  }

  .siteHeader .navbar-collapse .navMenu {
    padding: 20px;
    height: calc(100vh - 90px);
    overflow-y: auto;
  }
}

@media (width <= 767px) {
  .siteHeader .navbar-collapse {
    left: -100%;
    width: 100%;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle {
  border-radius: 0;
  padding: .5rem 1.5rem !important;
}

@media (width <= 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle {
    border-bottom: 1px solid #fff3;
    width: 100%;
    padding: 1.25rem 0 !important;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:after {
  display: block;
  background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drop-arrow.svg");
  background-repeat: no-repeat;
  background-size: 1.15rem;
  background-position: center;
  width: 1.15rem;
  height: 1.15rem;
  border: 0;
  transition: all .3s ease-in-out;
  margin-left: 1rem;
}

@media (width <= 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:after {
    margin-left: 0;
    position: absolute;
    right: 0;
  }
}

@media (width >= 1200px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle.show, .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:hover {
    background-color: #283f67;
    color: #fff;
  }

  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle.show:after, .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:hover:after {
    transform: rotate(180deg);
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown.show .dropdown-toggle:after {
  transform: rotate(180deg);
}

@media screen and (width <= 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu {
    position: static;
    border: 0;
    background-color: #0000;
    padding: 0;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu .nav-link {
  padding: .875rem 1.5rem;
  align-items: start;
  font-weight: 400 !important;
}

@media screen and (width <= 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu .nav-link {
    padding: .875rem 1rem;
  }
}

.siteHeader .navbar .navbar-toggler {
  background-color: #0000;
  margin-left: 0;
  padding: 0;
  position: relative;
  width: 24px;
  height: 18px;
}

.siteHeader .navbar .navbar-toggler:focus {
  box-shadow: none;
}

@media (width <= 1199px) {
  .siteHeader .navbar .navbar-toggler {
    margin-right: 13px;
  }
}

@media (width <= 767px) {
  .siteHeader .navbar .navbar-toggler {
    margin-right: 13px;
  }
}

.siteHeader .navbar .navbar-toggler:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 24px;
  background-color: #fff;
  height: 2px;
  transition: all .3s ease-in-out;
}

.siteHeader .navbar .navbar-toggler:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 24px;
  background-color: #fff;
  height: 2px;
  transition: all .3s ease-in-out;
}

.siteHeader .navbar .navbar-toggler .navbar-toggler-icon {
  background-image: none;
  height: 2px;
  background-color: #fff;
  width: 24px;
  transition: all .3s ease-in-out;
  display: flex;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle {
  color: #fff;
  border: 0;
  border-radius: .625rem;
  font-size: 1.25rem;
  padding: 0;
  display: flex;
  align-items: center;
  padding: .5rem .2rem !important;
}

@media (width <= 991px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle {
    font-size: 1.125rem;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle:after {
  display: none;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle.show svg path {
  fill: #00adef;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu {
  border-radius: .625rem;
  border: 1px solid #ffffff4d;
  min-width: 200px;
  position: absolute;
  top: 45px;
}

@media screen and (width <= 1199px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu {
    position: static;
    padding: 0;
    min-width: 100%;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item {
  font-size: 1.125rem;
  font-weight: 600;
  padding: .625rem 1rem;
  color: #fff;
}

@media (width <= 991px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item {
    font-size: 1rem;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item svg, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item img {
  margin-right: 10px;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item:hover, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.active, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item:focus {
  background: #283f67;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon {
  font-weight: 700;
  background: linear-gradient(to right, #283f67, #031940);
  color: #00b9ff;
  transition: none;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon svg path {
  fill: #00b9ff;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
  background: #283f67 !important;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu.show svg, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu.show img {
  width: 18px;
}

@media screen and (width <= 1199px) {
  .siteHeader .navbar .openmenuSidebar {
    border-bottom: 1px solid #ffffff80;
    padding: 30px 15px;
  }

  .siteHeader .navbar .openmenuSidebar .brandLogo {
    padding: 0;
  }

  .siteHeader .navbar .openmenuSidebar .brandLogo img {
    max-width: 150px;
  }

  .siteHeader .navbar .openmenuSidebar .navbar-toggler {
    position: absolute;
    right: 15px;
  }
}

.siteHeader.openmenu .navbar .navbar-toggler:after {
  transform: rotate(45deg)translate(-5px, -5px);
  background-color: #fff;
}

.siteHeader.openmenu .navbar .navbar-toggler:before {
  transform: rotate(-45deg)translate(-5px, 5px);
  background-color: #fff;
}

.siteHeader.openmenu .navbar .navbar-toggler .navbar-toggler-icon {
  opacity: 0;
}

.siteHeader .user_icon img {
  width: 26px;
  height: 26px;
}

@media screen and (width <= 767px) {
  .siteHeader .sidebar_backdrop {
    display: none;
  }
}

.languageDropdown {
  width: 64px;
}

@media (width <= 1199px) {
  .languageDropdown {
    width: 100%;
  }

  .languageDropdown .common_dropdown {
    width: 100%;
  }
}

.languageDropdown .common_dropdown .nav-link:hover, .languageDropdown .common_dropdown .nav-link.active, .languageDropdown .common_dropdown .nav-link:focus {
  color: #fff;
  background-color: #283f67 !important;
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle {
  color: #fff;
  border: 0;
  font-size: 1.25rem;
  padding: 0;
  display: flex;
  align-items: center;
  border-radius: 0 !important;
}

@media (width <= 991px) {
  .languageDropdown .common_dropdown.dropdown .dropdown-toggle {
    font-size: 1rem;
  }
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle svg {
  margin-right: 10px;
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle:focus, .languageDropdown .common_dropdown.dropdown .dropdown-toggle:hover {
  background-color: #0000 !important;
}

@media (width <= 1199px) {
  .languageDropdown .common_dropdown.dropdown .dropdown-toggle {
    width: 100%;
  }
}

.languageDropdown .globalIcon .icon {
  transition: opacity .3s;
}

.languageDropdown .globalIcon .blue {
  display: none;
}

.languageDropdown .nav-item:hover .globalIcon .black, .languageDropdown .nav-item.show .globalIcon .black {
  display: none;
}

.languageDropdown .nav-item:hover .globalIcon .blue, .languageDropdown .nav-item.show .globalIcon .blue {
  display: block;
}

.userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name {
  display: none;
}

@media screen and (width <= 1199px) {
  .userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name {
    display: block;
    padding-left: 10px;
    font-size: 18px;
  }

  .userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name svg {
    width: 26px;
    height: 26px;
  }

  .userDropdown.common_dropdown.dropdown .dropdown-toggle {
    border-bottom: 0 !important;
  }
}

.userDropdown.common_dropdown.dropdown .dropdown-toggle:hover {
  background-color: #0000 !important;
}

@media (width <= 1199px) {
  .brandLogo {
    display: flex;
  }

  .brandLogo img {
    max-width: 150px;
  }
}

@media (width <= 767px) {
  .brandLogo img {
    max-width: 110px;
  }
}

@media (width <= 359px) {
  .brandLogo img {
    max-width: 100px;
  }
}

.sidebar_backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 1000;
  background-color: #0003;
  transition: all .2s ease-in-out;
}

.image_color_to_white {
  filter: brightness(0) invert();
}

@media (width >= 1200px) {
  .nav-link:hover, .nav-link.active, .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }
}


/* [project]/src/css/common/Footer.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.site_footer {
  background-color: #000;
}

.site_footer_inner {
  padding: 70px 0;
}

@media screen and (width <= 991px) {
  .site_footer_inner {
    padding: 40px 0;
  }
}

.site_footer_logo img {
  width: 200px;
}

.site_footer_content p {
  color: #ffffffa6;
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
  letter-spacing: -.1px;
  margin-top: 20px;
}

@media screen and (width <= 991px) {
  .site_footer_content p {
    font-size: 16px;
  }
}

@media screen and (width <= 767px) {
  .site_footer_links {
    margin-top: 20px;
  }
}

.site_footer_links h4 {
  color: #c5c5d5;
  margin-bottom: 1.25rem;
  font-size: 1.65rem;
  line-height: 35px;
  font-weight: 600;
}

@media screen and (width <= 991px) {
  .site_footer_links h4 {
    font-size: 18px;
  }
}

.site_footer_links ul li a {
  font-size: 20px;
  font-weight: 600;
  line-height: 24.5px;
  letter-spacing: -.1px;
  color: #fff;
  transition: all .3s ease-in-out;
  padding-bottom: 10px;
}

@media screen and (width <= 991px) {
  .site_footer_links ul li a {
    font-size: 16px;
  }
}

.site_footer_links ul li a:hover, .site_footer_links ul li a.active {
  color: #00adef;
}

.site_footer_copyright {
  padding: 1.25rem 0;
  border-top: 1px solid #fff;
}

.site_footer_copyright p {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
  letter-spacing: -.1px;
}

@media screen and (width <= 991px) {
  .site_footer_copyright p {
    font-size: 16px;
  }
}


/* [project]/src/css/Home/Pricing.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.pricing {
  position: relative;
}

.pricing_banner {
  padding: 100px 0 42px;
}

@media (width <= 767px) {
  .pricing_banner {
    padding: 50px 0 42px;
  }
}

.pricing_banner_content h1 {
  font-size: 80px;
  font-weight: 800;
  line-height: 90px;
}

@media screen and (width <= 1199px) {
  .pricing_banner_content h1 {
    font-size: 60px;
    line-height: 65px;
  }
}

@media screen and (width <= 991px) {
  .pricing_banner_content h1 {
    font-size: 48px;
    line-height: 52.8px;
    font-weight: 800;
  }
}

@media screen and (width <= 767px) {
  .pricing_banner_content h1 {
    text-align: center;
  }
}

.pricing_banner_content p {
  font-size: 24px;
  font-weight: 600;
  line-height: 36px;
  letter-spacing: -1px;
  text-align: left;
  padding-top: 2rem;
}

@media screen and (width <= 991px) {
  .pricing_banner_content p {
    font-size: 18px;
    line-height: 27px;
  }
}

@media screen and (width <= 767px) {
  .pricing_banner_content p {
    text-align: center;
    margin-bottom: 20px;
    padding-top: 1.25rem;
  }
}

.pricing_banner_forever {
  background: linear-gradient(139.01deg, #26334d 18.26%, #4e5d7a 63.25%, #26334d 100.07%), radial-gradient(27.58% 27.58%, #fea501 0% 100%);
  border-radius: 40px;
  padding: 50px 20px;
  text-align: center;
  position: relative;
  margin: auto;
  max-width: 22em;
  box-sizing: border-box;
  background-clip: padding-box;
  border: 2px solid #0000;
}

.pricing_banner_forever:before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  margin: -2px;
  border-radius: inherit;
  background: linear-gradient(142.34deg, #00adef .06%, #fea50000 46.5%, #00adef 98.85%);
}

@media (width <= 991px) {
  .pricing_banner_forever h4 {
    font-size: 20px;
    line-height: 25px;
  }
}

@media (width <= 767px) {
  .pricing_banner_forever {
    padding: 30px 20px;
  }
}

.pricing_table {
  position: relative;
  z-index: 1;
  padding-bottom: 7rem;
}

.pricing_table:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-crypto-stock-analysis.png");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100%;
  border-radius: 2rem;
  z-index: -1;
}

.pricing_table_switch {
  margin-bottom: 25px;
}

.pricing_table_switch p {
  font-size: 20px;
  font-weight: 600;
  line-height: 36px;
  text-align: left;
}

.pricing_table_switch .checkbox_input {
  margin: 0 20px;
}

.pricing_table_col:first-child .pricing_table_box {
  border-right: 0;
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
}

.pricing_table_col:last-child .pricing_table_box {
  border-left: 0;
  border-top-right-radius: 50px;
  border-bottom-right-radius: 50px;
}

.pricing_table_box {
  background: #0003;
  border: 3px solid #ffffff1a;
  padding: 50px 20px;
  width: 100%;
}

@media (width >= 1400px) {
  .pricing_table_box {
    padding: 50px 30px;
  }
}

@media (width <= 991px) {
  .pricing_table_box {
    border-radius: 30px;
    margin-top: 30px;
    border: 3px solid #ffffff1a !important;
  }
}

@media (width <= 767px) {
  .pricing_table_box {
    padding: 30px 20px;
  }
}

.pricing_table_box_heading {
  text-align: center;
}

.pricing_table_box_heading h2 {
  font-size: 48px;
  font-weight: 800;
  text-align: center;
  margin: 20px 0;
}

@media screen and (width <= 991px) {
  .pricing_table_box_heading h2 {
    font-size: 36px;
  }
}

@media screen and (width <= 767px) {
  .pricing_table_box_heading h2 {
    font-size: 28px;
  }
}

.pricing_table_box_heading h2 span {
  font-size: 24px;
}

@media screen and (width <= 991px) {
  .pricing_table_box_heading h2 span {
    font-size: 18px;
  }
}

@media screen and (width <= 767px) {
  .pricing_table_box_heading h2 span {
    font-size: 16px;
  }
}

.pricing_table_box_heading p {
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
  text-align: center;
  margin: 20px 0;
}

.pricing_table_box_heading p a:hover {
  color: #32cd33;
}

@media screen and (width <= 991px) {
  .pricing_table_box_heading .green-btn {
    width: 100%;
  }
}

.pricing_table_box ul {
  margin-top: 30px;
}

@media (width >= 768px) and (width <= 991px) {
  .pricing_table_box ul {
    display: flex;
    flex-wrap: wrap;
  }
}

.pricing_table_box ul li {
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
  margin: 20px 0;
  color: #fff;
}

@media (width >= 768px) and (width <= 991px) {
  .pricing_table_box ul li {
    width: 50%;
  }
}

.pricing_table_box ul li svg {
  margin-right: 10px;
}

.pricing_col {
  display: flex;
}

.pricing_box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.pricing ul {
  flex-grow: 1;
}


/* [project]/src/css/common/CommonTooltip.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.tooltip-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.tooltip-container img {
  max-width: 20px;
  min-width: 20px;
  min-height: 20px;
  max-height: 20px;
}

.tooltip-wrapper {
  flex-shrink: 0;
  cursor: pointer;
}

.tooltip-box {
  position: absolute;
  padding: 5px 10px;
  z-index: 1000;
  min-width: 300px;
  width: 300px;
  background-color: #032e59b2;
  color: #fff;
  text-align: left;
  padding: 10px 15px;
  border-radius: 5px;
  backdrop-filter: blur(6px);
  pointer-events: auto !important;
}

.tooltip-box p, .tooltip-box a {
  font-weight: 300;
  line-height: 20px;
  font-size: .875rem !important;
}

@media screen and (width <= 991px) {
  .tooltip-box p, .tooltip-box a {
    font-size: 14px;
    line-height: 18px;
  }

  .tooltip-box {
    min-width: 200px;
    width: 200px;
  }
}

.tooltip-top-left {
  top: 0;
  right: 0;
  transform: translateY(-100%);
}

.tooltip-top-right {
  top: 0;
  left: 0;
  transform: translateY(-100%);
}

.tooltip-bottom-left {
  bottom: 0;
  right: 0;
  transform: translateY(100%);
}

.tooltip-bottom-right {
  bottom: 0;
  left: 0;
  transform: translateY(100%);
}

.tooltip-center-bottom {
  top: 25px;
  left: 50%;
  transform: translateX(-50%);
}

.tooltip-center-top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%)translateY(-10px);
}


/*# sourceMappingURL=src_css_7eef8aad._.css.map*/
