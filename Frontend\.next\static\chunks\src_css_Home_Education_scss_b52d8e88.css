/* [project]/src/css/Home/Education.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.education {
  padding: 5rem 0;
}

@media (width <= 991px) {
  .education {
    padding-top: 40px !important;
  }
}

.education .container {
  max-width: 1080px;
}

.education_heading p {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 28px;
  letter-spacing: -.1px;
  margin: 30px 0;
}

@media (width <= 991px) {
  .education_heading p {
    font-size: 1rem;
    line-height: 22px;
    margin: 20px 0;
  }
}

.education_heading h1 {
  font-size: 3rem;
  font-weight: 800;
}

@media (width <= 1199px) {
  .education_heading h1 {
    font-size: 2.5rem;
  }
}

@media (width <= 767px) {
  .education_heading h1 {
    font-size: 1.5rem;
  }
}

@media (width <= 390px) {
  .education_heading h1 {
    font-size: 1.3rem;
  }
}

.education_search .commonSearch {
  margin: 0 auto;
  max-width: 400px;
}

.education_search .commonSearch .form-control {
  width: 100%;
}

.education_fliters {
  padding: 30px 0 80px;
}

@media (width <= 991px) {
  .education_fliters {
    padding: 20px 0 30px;
  }
}

@media (width <= 767px) {
  .education_fliters {
    padding: 20px 0 10px;
  }
}

.education_fliters_inner {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
}

@media (width <= 767px) {
  .education_fliters_inner {
    margin-bottom: 20px;
  }
}

.education_fliters_boxbutton {
  width: 30px;
  height: 30px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  margin-bottom: 8px;
  color: #fff;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.5rem;
  letter-spacing: -.1px;
  background-color: #00adef26;
  border: 0;
  transition: all .3s ease-in-out;
}

@media (width <= 767px) {
  .education_fliters_boxbutton {
    width: 26px;
    height: 26px;
    font-size: .875rem;
  }
}

.education_fliters_boxbutton:last-child {
  margin-right: 0;
}

.education_fliters_boxbutton:hover, .education_fliters_boxbutton.active {
  background-color: #00adef;
}

.education_fliters_boxadd {
  padding: 5px 15px;
  background-color: #00adef;
  border-radius: 15px;
  display: inline-flex;
}

.education_fliters_boxadd h6 {
  color: #fff;
  display: flex;
  align-items: center;
}

.education_fliters_boxadd h6 img {
  cursor: pointer;
}

.education_pagination {
  display: flex;
  justify-content: flex-end;
}

.education .education {
  padding: 2rem 0;
}

.education .education_term_head {
  padding: 10px 5px;
  margin: 0;
}

.education .education_term_list {
  border-top: 1px solid #666;
  padding: 15px 5px;
  margin: 0;
}

.education .education_term_list:last-child {
  border-bottom: 1px solid #666;
}

.education .education_term_list p {
  color: #f5f5f5;
  font-size: 15px;
  font-weight: 400;
  line-height: 22.5px;
  text-align: left;
}

@media (width <= 767px) {
  .education .education_term_list p {
    margin-top: 1.25rem;
  }
}

.education .education_term_list .read_more_button {
  min-height: 46px;
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
  min-width: 120px;
  border-radius: 10px;
  padding: 8px 1rem;
}

/*# sourceMappingURL=src_css_Home_Education_scss_b52d8e88.css.map*/