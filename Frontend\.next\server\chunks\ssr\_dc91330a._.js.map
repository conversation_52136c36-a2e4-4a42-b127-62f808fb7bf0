{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Header.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Header.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Header.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Header.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Header.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Header.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Footer.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Footer.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Footer.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Footer.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Footer.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Footer.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Layouts/HomeLayout.js"], "sourcesContent": ["// 'use client';\r\n\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { Fragment } from \"react\";\r\nimport Header from \"@/Components/UI/Header\";\r\nimport Footer from \"@/Components/UI/Footer\";\r\nimport { LanguageProvider } from \"@/context/LanguageContext\";\r\n\r\nconst HomeLayout = ({ children }) => {\r\n  return (\r\n    <Fragment>\r\n      <LanguageProvider>\r\n        <Header />\r\n        {/* Wrap main content in <main> for SEO & accessibility */}\r\n        <main className=\"main-content\">{children}</main>\r\n        <Footer />\r\n      </LanguageProvider>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default HomeLayout;\r\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;AAGhB;AACA;AACA;AACA;;;;;;;AAEA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAE;IAC9B,qBACE,8OAAC,qMAAA,CAAA,WAAQ;kBACP,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;;8BACf,8OAAC,iIAAA,CAAA,UAAM;;;;;8BAEP,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;8BAChC,8OAAC,iIAAA,CAAA,UAAM;;;;;;;;;;;;;;;;AAIf;uCAEe", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/constants/index.js"], "sourcesContent": ["export const placeHolderImg = \"/images/tradereply-placeholder.jpg\";\r\nexport const blogLimit = 300;\r\n\r\nexport const SYSTEM_ROLES = {\r\n  ADMIN: \"admin\",\r\n  USER: \"user\",\r\n  SUPER_ADMIN: \"Super admin\"\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,iBAAiB;AACvB,MAAM,YAAY;AAElB,MAAM,eAAe;IAC1B,OAAO;IACP,MAAM;IACN,aAAa;AACf", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/common/Home/RecentPost.js"], "sourcesContent": ["import { blogLimit, placeHolderImg } from \"@/constants\";\r\nimport \"../../../css/Home/Blog.scss\"; \r\nimport Link from \"next/link\"; // Import Next.js Link\r\n \r\n\r\nconst RecentPost = ({ img, title, text, coinname=null, className, time=null, href, category=null }) => {\r\n  \r\n  return (\r\n    <>\r\n      <Link href={href} className={`recent_post ${className}`}>\r\n        <div className=\"recent_post_img\">\r\n          <img src={img || placeHolderImg} \r\n          alt={title ? `Recent blog post: ${title}` : \"TradeReply blog article thumbnail\"}\r\n           />\r\n        </div>\r\n        <div className=\"recent_post_content\">\r\n          <h4>{title}</h4>\r\n          <p>{text ?? \"N/A\"}</p>\r\n          <small>{coinname}</small>\r\n          {time && <span className=\"recent_post_time\">{time}</span>}\r\n        </div>\r\n      </Link>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default RecentPost;\r\n\r\n\r\n     {/* <p>{truncateContent(text, blogLimit)}</p> */}\r\n          {/* <p>{text || \"No Text Available\"}</p> */}\r\n          {/* <p dangerouslySetInnerHTML={{ __html: text }} />  */}"], "names": [], "mappings": ";;;;AAAA;AAEA,8QAA8B,sBAAsB;;;;;AAGpD,MAAM,aAAa,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,WAAS,IAAI,EAAE,SAAS,EAAE,OAAK,IAAI,EAAE,IAAI,EAAE,WAAS,IAAI,EAAE;IAEhG,qBACE;kBACE,cAAA,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM;YAAM,WAAW,CAAC,YAAY,EAAE,WAAW;;8BACrD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,KAAK,OAAO,yHAAA,CAAA,iBAAc;wBAC/B,KAAK,QAAQ,CAAC,kBAAkB,EAAE,OAAO,GAAG;;;;;;;;;;;8BAG9C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAI;;;;;;sCACL,8OAAC;sCAAG,QAAQ;;;;;;sCACZ,8OAAC;sCAAO;;;;;;wBACP,sBAAQ,8OAAC;4BAAK,WAAU;sCAAoB;;;;;;;;;;;;;;;;;;;AAKvD;uCAEe;AAGV,CAAgD,CAC3C,CAA2C,CAC3C,CAAwD", "debugId": null}}, {"offset": {"line": 252, "column": 6}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/CustomBreadcrumb.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/CustomBreadcrumb.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/CustomBreadcrumb.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/CustomBreadcrumb.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/CustomBreadcrumb.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/CustomBreadcrumb.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/blog/%5Bdetail%5D/components/BlogContent.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/blog/[detail]/components/BlogContent.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/blog/[detail]/components/BlogContent.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0T,GACvV,wFACA", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/blog/%5Bdetail%5D/components/BlogContent.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/blog/[detail]/components/BlogContent.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/blog/[detail]/components/BlogContent.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/blog/%5Bdetail%5D/page.js"], "sourcesContent": ["import { unstable_noStore as noStore } from 'next/cache';\r\nimport HomeLayout from \"@/Layouts/HomeLayout\";\r\nimport RecentPost from \"@/Components/common/Home/RecentPost\";\r\nimport { Container } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\nimport \"../../../../css/Home/BlogDetail.scss\";\r\nimport CustomBreadcrumb from \"@/Components/UI/CustomBreadcrumb\";\r\nimport dayjs from \"dayjs\";\r\nimport relativeTime from \"dayjs/plugin/relativeTime\";\r\ndayjs.extend(relativeTime);\r\nimport BlogContent from \"./components/BlogContent\";\r\nimport DOMPurify from \"dompurify\";\r\nimport JsonLdSchema, { generateBlogPostingSchema, formatDateToISO, getBlogSlug, cleanKeywords } from \"@/Seo/Schema/JsonLdSchema\";\r\n\r\nasync function fetchBlog(detail) {\r\n  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/article/blog/${detail}`);\r\n\r\n  if (!res.ok) {\r\n      throw new Error(`API error: ${res.status}`);\r\n  }\r\n\r\n  return res.json();\r\n}\r\n\r\nexport default async function BlogDetail({ params }) {\r\n  noStore();\r\n\r\n  const resolvedParams = await params;\r\n  const detail = resolvedParams.detail;\r\n\r\n  const response = await fetchBlog(detail);\r\n  const blog = response?.data;\r\n\r\n  // Generate JSON-LD schema for blog article if blog data exists\r\n  let blogSchema = null;\r\n  if (blog) {\r\n    const blogSlug = getBlogSlug(blog);\r\n    const canonicalUrl = `https://www.tradereply.com/blog/${blogSlug}`;\r\n\r\n    blogSchema = generateBlogPostingSchema({\r\n      canonicalUrl,\r\n      headline: blog.title,\r\n      description: blog.summary,\r\n      imageUrl: blog.feature_image_url,\r\n      datePublished: formatDateToISO(blog.created_at),\r\n      dateModified: formatDateToISO(blog.updated_at),\r\n      // These fields will use fallback generation if not available from backend\r\n      articleBody: blog.schema_article_body,\r\n      keywords: blog.schema_keywords,\r\n      blogData: blog // Pass full blog data for fallback generation\r\n    });\r\n  }\r\n\r\n   return (\r\n      <>\r\n        {blogSchema && <JsonLdSchema schemas={[blogSchema]} />}\r\n        <BlogContent\r\n          response={response}\r\n        />\r\n      </>\r\n    );\r\n}\r\n\r\nexport async function generateMetadata({ params }) {\r\n  noStore();\r\n  const resolvedParams = await params;\r\n  const detail = resolvedParams.detail;\r\n  const response = await fetchBlog(detail);\r\n  const blog = response.data;\r\n  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;\r\n\r\n return {\r\n   title: blog.title\r\n     ? `${blog.title} | TradeReply Blog`\r\n     : 'TradeReply Blog | Insights & Strategies for Traders',\r\n   description: blog.summary || 'Explore insights on TradeReply.',\r\n   robots: \"index, follow\",\r\n   openGraph: {\r\n     title: `${blog.title} | TradeReply Blog`,\r\n     description: blog?.summary,\r\n     siteName: 'TradeReply',\r\n     type: 'article',\r\n     images: [\r\n       {\r\n         url: blog?.feature_image_url || 'https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg', // Replace with actual default image if needed\r\n         width: 1200,\r\n         height: 630,\r\n       },\r\n     ],\r\n     locale: 'en_US',\r\n   },\r\n   twitter: {\r\n     title: `${blog?.title} | TradeReply Blog`,\r\n     description: blog?.summary,\r\n     site: '@JoinTradeReply',\r\n     images: [blog?.feature_image_url || 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'], // Replace with actual default image if needed\r\n   },\r\n   icons: {\r\n         icon: [\r\n           {\r\n             url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,\r\n             type: \"image/x-icon\",\r\n           },\r\n           {\r\n             url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,\r\n             type: \"image/svg+xml\",\r\n           },\r\n         ],\r\n       },\r\n };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;;AAHA,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,+IAAA,CAAA,UAAY;;;;AAKzB,eAAe,UAAU,MAAM;IAC7B,MAAM,MAAM,MAAM,MAAM,6DAAwC,qBAAqB,EAAE,QAAQ;IAE/F,IAAI,CAAC,IAAI,EAAE,EAAE;QACT,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,MAAM,EAAE;IAC9C;IAEA,OAAO,IAAI,IAAI;AACjB;AAEe,eAAe,WAAW,EAAE,MAAM,EAAE;IACjD,CAAA,GAAA,6HAAA,CAAA,mBAAO,AAAD;IAEN,MAAM,iBAAiB,MAAM;IAC7B,MAAM,SAAS,eAAe,MAAM;IAEpC,MAAM,WAAW,MAAM,UAAU;IACjC,MAAM,OAAO,UAAU;IAEvB,+DAA+D;IAC/D,IAAI,aAAa;IACjB,IAAI,MAAM;QACR,MAAM,WAAW,CAAA,GAAA,oIAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,MAAM,eAAe,CAAC,gCAAgC,EAAE,UAAU;QAElE,aAAa,CAAA,GAAA,oIAAA,CAAA,4BAAyB,AAAD,EAAE;YACrC;YACA,UAAU,KAAK,KAAK;YACpB,aAAa,KAAK,OAAO;YACzB,UAAU,KAAK,iBAAiB;YAChC,eAAe,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU;YAC9C,cAAc,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU;YAC7C,0EAA0E;YAC1E,aAAa,KAAK,mBAAmB;YACrC,UAAU,KAAK,eAAe;YAC9B,UAAU,KAAK,8CAA8C;QAC/D;IACF;IAEC,qBACG;;YACG,4BAAc,8OAAC,oIAAA,CAAA,UAAY;gBAAC,SAAS;oBAAC;iBAAW;;;;;;0BAClD,8OAAC,yKAAA,CAAA,UAAW;gBACV,UAAU;;;;;;;;AAIpB;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAE;IAC/C,CAAA,GAAA,6HAAA,CAAA,mBAAO,AAAD;IACN,MAAM,iBAAiB,MAAM;IAC7B,MAAM,SAAS,eAAe,MAAM;IACpC,MAAM,WAAW,MAAM,UAAU;IACjC,MAAM,OAAO,SAAS,IAAI;IAC1B,MAAM;IAEP,OAAO;QACL,OAAO,KAAK,KAAK,GACb,GAAG,KAAK,KAAK,CAAC,kBAAkB,CAAC,GACjC;QACJ,aAAa,KAAK,OAAO,IAAI;QAC7B,QAAQ;QACR,WAAW;YACT,OAAO,GAAG,KAAK,KAAK,CAAC,kBAAkB,CAAC;YACxC,aAAa,MAAM;YACnB,UAAU;YACV,MAAM;YACN,QAAQ;gBACN;oBACE,KAAK,MAAM,qBAAqB;oBAChC,OAAO;oBACP,QAAQ;gBACV;aACD;YACD,QAAQ;QACV;QACA,SAAS;YACP,OAAO,GAAG,MAAM,MAAM,kBAAkB,CAAC;YACzC,aAAa,MAAM;YACnB,MAAM;YACN,QAAQ;gBAAC,MAAM,qBAAqB;aAA0E;QAChH;QACA,OAAO;YACD,MAAM;gBACJ;oBACE,KAAK,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;oBACnF,MAAM;gBACR;gBACA;oBACE,KAAK,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;oBACnF,MAAM;gBACR;aACD;QACH;IACN;AACD", "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}