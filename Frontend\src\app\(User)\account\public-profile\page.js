"use client";
import { Col, Row, Dropdown } from "react-bootstrap";
import React from "react";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import UserInformation from "@/Components/common/Account/PublicProfile/UserInformation";
import UserAbout from "@/Components/common/Account/PublicProfile/UserAbout";
import FollowersList from "@/Components/common/Account/PublicProfile/FollowersList";
import FollowingList from "@/Components/common/Account/PublicProfile/FollowingList";
import MarketFeedBack from "@/Components/common/Account/SellerDashboard/MarketFeedBack";
import ActiveListings from "@/Components/common/Account/PublicProfile/ActiveListings";
import { RightArrowIconSvg } from "@/assets/svgIcons/SvgIcon";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/MarketPlace.scss";

export default function PublicProfile() {
  const metaArray = {
    noindex: true,
    title: "Account Overview | TradeReply",
    description:
      "View your account overview on TradeReply.com. Access a summary of your account details, recent activity, and important notifications.",
    canonical_link: "https://www.tradereply.com/account/overview",
    og_site_name: "TradeReply",
    og_title: "Account Overview | TradeReply",
    og_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
    twitter_title: "Account Overview | TradeReply",
    twitter_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account">
          <div className="d-flex justify-between align-items-center">
            <SidebarHeading
              title="Public Profile"
              Linktext="View public profile"
              Linkicon={<RightArrowIconSvg />}
            />
          </div>
          <Row className="mb-4 mb-lg-4">
            <Col md={6} xs={12} className="d-flex">
              <UserInformation />
            </Col>
            <Col md={6} xs={12} className="d-flex mt-4 mt-md-0">
              <UserAbout />
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col md={6} xs={12} className="d-flex">
              <FollowersList />
            </Col>
            <Col md={6} xs={12} className="d-flex mt-4 mt-md-0">
              <FollowingList />
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col md={6} xs={12} className="d-flex">
              <MarketFeedBack />
            </Col>
            <Col md={6} xs={12} className="d-flex mt-4 mt-md-0">
              <ActiveListings />
            </Col>
          </Row>
        </div>
      </AccountLayout>
    </>
  );
}
