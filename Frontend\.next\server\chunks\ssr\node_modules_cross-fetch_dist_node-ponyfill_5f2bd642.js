module.exports = {

"[project]/node_modules/cross-fetch/dist/node-ponyfill.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_dd57d255._.js",
  "server/chunks/ssr/[externals]_punycode_a50ddb3c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/cross-fetch/dist/node-ponyfill.js [app-ssr] (ecmascript)");
    });
});
}}),

};