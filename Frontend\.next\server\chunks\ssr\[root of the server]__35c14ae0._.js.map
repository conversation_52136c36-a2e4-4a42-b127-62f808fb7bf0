{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/inactivityHandler.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useRef, useState } from \"react\";\r\nimport Cookies from \"js-cookie\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nconst MAX_INACTIVITY_DURATION = 30 * 60 * 1000;\r\n\r\nconst InactivityHandler = () => {\r\n  const router = useRouter();\r\n  const timeoutRef = useRef(null);\r\n  const [hasLoggedOut, setHasLoggedOut] = useState(false);\r\n\r\n\r\n\r\n  const logoutUser = () => {\r\n    if (hasLoggedOut) return;\r\n    setHasLoggedOut(true);\r\n\r\n    const token = Cookies.get(\"authToken\");\r\n    const shouldSetSessionExpired = !!token;\r\n\r\n    // Clear cookies and storage first\r\n    Cookies.remove(\"authToken\");\r\n    localStorage.removeItem(\"lastActivity\");\r\n    if (shouldSetSessionExpired) {\r\n      sessionStorage.setItem(\"sessionExpired\", \"true\");\r\n    }\r\n    localStorage.setItem(\"loggedOut\", Date.now());\r\n    \r\n    // Then call the API\r\n    fetch(\"/api/logout\", {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n    }).finally(() => {\r\n      router.replace(\"/login\");\r\n    });\r\n  };\r\n\r\n  const updateLastActivity = () => {\r\n    const now = Date.now();\r\n    localStorage.setItem(\"lastActivity\", now.toString());\r\n\r\n    if (timeoutRef.current) clearTimeout(timeoutRef.current);\r\n    timeoutRef.current = setTimeout(logoutUser, MAX_INACTIVITY_DURATION);\r\n  };\r\n\r\n  const checkInactivity = () => {\r\n    const token = Cookies.get(\"authToken\");\r\n    // If no token, don't start inactivity timer\r\n    if (!token) return;\r\n\r\n    const lastActivity = parseInt(localStorage.getItem(\"lastActivity\") || \"0\", 10);\r\n    const now = Date.now();\r\n\r\n    if (!lastActivity || now - lastActivity > MAX_INACTIVITY_DURATION) {\r\n      logoutUser();\r\n    } else {\r\n      updateLastActivity();\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const token = Cookies.get(\"authToken\");\r\n\r\n    // Only monitor inactivity if user is logged in\r\n    if (!token) return;\r\n\r\n    checkInactivity();\r\n\r\n    const handleActivity = () => updateLastActivity();\r\n\r\n    const activityEvents = [\"mousemove\", \"keydown\", \"click\", \"scroll\"];\r\n    activityEvents.forEach((event) => window.addEventListener(event, handleActivity));\r\n\r\n    const handleStorage = (e) => {\r\n      if (e.key === \"loggedOut\") {\r\n        logoutUser();\r\n      }\r\n    };\r\n    window.addEventListener(\"storage\", handleStorage);\r\n\r\n    return () => {\r\n      activityEvents.forEach((event) => window.removeEventListener(event, handleActivity));\r\n      window.removeEventListener(\"storage\", handleStorage);\r\n      if (timeoutRef.current) clearTimeout(timeoutRef.current);\r\n    };\r\n  }, []);\r\n\r\n  return null;\r\n};\r\n\r\nexport default InactivityHandler;\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAJA;;;;AAMA,MAAM,0BAA0B,KAAK,KAAK;AAE1C,MAAM,oBAAoB;IACxB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAIjD,MAAM,aAAa;QACjB,IAAI,cAAc;QAClB,gBAAgB;QAEhB,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAC1B,MAAM,0BAA0B,CAAC,CAAC;QAElC,kCAAkC;QAClC,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,aAAa,UAAU,CAAC;QACxB,IAAI,yBAAyB;YAC3B,eAAe,OAAO,CAAC,kBAAkB;QAC3C;QACA,aAAa,OAAO,CAAC,aAAa,KAAK,GAAG;QAE1C,oBAAoB;QACpB,MAAM,eAAe;YACnB,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD,GAAG,OAAO,CAAC;YACT,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,MAAM,qBAAqB;QACzB,MAAM,MAAM,KAAK,GAAG;QACpB,aAAa,OAAO,CAAC,gBAAgB,IAAI,QAAQ;QAEjD,IAAI,WAAW,OAAO,EAAE,aAAa,WAAW,OAAO;QACvD,WAAW,OAAO,GAAG,WAAW,YAAY;IAC9C;IAEA,MAAM,kBAAkB;QACtB,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAC1B,4CAA4C;QAC5C,IAAI,CAAC,OAAO;QAEZ,MAAM,eAAe,SAAS,aAAa,OAAO,CAAC,mBAAmB,KAAK;QAC3E,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI,CAAC,gBAAgB,MAAM,eAAe,yBAAyB;YACjE;QACF,OAAO;YACL;QACF;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAE1B,+CAA+C;QAC/C,IAAI,CAAC,OAAO;QAEZ;QAEA,MAAM,iBAAiB,IAAM;QAE7B,MAAM,iBAAiB;YAAC;YAAa;YAAW;YAAS;SAAS;QAClE,eAAe,OAAO,CAAC,CAAC,QAAU,OAAO,gBAAgB,CAAC,OAAO;QAEjE,MAAM,gBAAgB,CAAC;YACrB,IAAI,EAAE,GAAG,KAAK,aAAa;gBACzB;YACF;QACF;QACA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,OAAO;YACL,eAAe,OAAO,CAAC,CAAC,QAAU,OAAO,mBAAmB,CAAC,OAAO;YACpE,OAAO,mBAAmB,CAAC,WAAW;YACtC,IAAI,WAAW,OAAO,EAAE,aAAa,WAAW,OAAO;QACzD;IACF,GAAG,EAAE;IAEL,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/auth.js"], "sourcesContent": ["import axios from 'axios';\r\nimport Cookies from 'js-cookie';\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nconst Url = process.env.NEXT_PUBLIC_API_BASE_URL;\r\nconst apiUrl = `${Url}/api/v1/auth`;\r\n\r\nexport const register = async (userData) => {\r\n  try {\r\n    const response = await axios.post(`${apiUrl}/register`, userData, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      withCredentials: true,\r\n    });\r\n\r\n    return response.data; // Return success response\r\n  } catch (error) {\r\n    console.log(error?.response?.data); // Log full response\r\n\r\n    if (error.response && error.response.status === 422) {\r\n      return {\r\n        success: false,\r\n        errors: error.response.data.errors || {},\r\n        message: error.response.data.message,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      errors: { general: \"Something went wrong. Please try again.\" },\r\n    };\r\n  }\r\n};\r\n\r\n\r\nexport const resendVerificationCode = async (payload) => {\r\n  try {\r\n    const response = await axios.post(`${apiUrl}/resend-verification-code`, payload);\r\n    return response.data;\r\n  } catch (error) {\r\n    return { errors: error.response?.data?.errors || {} };\r\n  }\r\n};\r\n\r\nexport const verifyEmailToken = async (payload) => {\r\n  try {\r\n    const response = await axios.get(`${apiUrl}/verify-token`, {\r\n      params: payload,\r\n      headers: { \"Content-Type\": \"application/json\" }\r\n    });\r\n\r\n    return response.data;\r\n  } catch (error) {\r\n    return {\r\n      success: false,\r\n      message: error.response?.data?.message || \"Invalid token\"\r\n    };\r\n  }\r\n};\r\n\r\nexport const createUsername = async (payload) => {\r\n  try {\r\n    const response = await axios.post(`${apiUrl}/create-username`, payload);\r\n    return response.data;\r\n  } catch (error) {\r\n    return { error };\r\n  }\r\n};\r\n\r\nexport const forgotPassword = async (type, value, uuid) => {\r\n  try {\r\n    const response = await axios.post(`${apiUrl}/forgot-password`, { type, value, uuid });\r\n    return response.data;\r\n  } catch (error) {\r\n    if (error.response) {\r\n      return { success: false, message: error.response.data.message || \"Failed to send password reset link\" };\r\n    }\r\n    return { success: false, message: \"Something went wrong. Please try again later.\" };\r\n  }\r\n};\r\n\r\nexport const resetPassword = async (newPassword) => {\r\n  try {\r\n    const resetPasswordData = JSON.parse(sessionStorage.getItem(\"reset_password_data\"));\r\n\r\n    if (!resetPasswordData || !resetPasswordData.uuid) {\r\n      throw new Error(\"No reset data found\");\r\n    }\r\n\r\n    const { uuid } = resetPasswordData;\r\n\r\n    const response = await axios.post(`${apiUrl}/reset-password`, {\r\n      uuid,\r\n      new_password: newPassword,\r\n    });\r\n\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Error resetting password\", error);\r\n    return { success: false, message: error.response?.data?.message || \"Error resetting password\" };\r\n  }\r\n};\r\n\r\nexport const login = async (credentials) => {\r\n  try {\r\n    await axios.get(`${Url}/sanctum/csrf-cookie`, { withCredentials: true });\r\n\r\n    const response = await axios.post(`${apiUrl}/login`, credentials, {\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      withCredentials: true,\r\n    });\r\n\r\n    localStorage.setItem(\"lastActivity\", Date.now().toString());\r\n\r\n    console.log('auth.js response.data', response.data);\r\n    return response.data;\r\n\r\n  } catch (error) {\r\n    console.error(\"Login error authjs:\", error.response);\r\n\r\n    return {\r\n      success: false,\r\n      message: error.response?.data?.message || \"Invalid credentials\",\r\n      errors: error.response?.data?.errors || [],\r\n      captcha_required:\r\n        error.response?.data?.type === 'captcha'\r\n          ? error.response?.data?.state\r\n          : false,\r\n      lockout_redirect:\r\n        error.response?.data?.type === 'redirect'\r\n          ? error.response?.data?.state\r\n          : false,\r\n      redirect_to: \"/locate-account\"\r\n    };\r\n  }\r\n};\r\n\r\nexport const logout = async () => {\r\n  try {\r\n    await axios.post(`${apiUrl}/logout`, {}, {\r\n      headers: {\r\n        Authorization: `Bearer ${Cookies.get(\"authToken\")}`\r\n      }\r\n    });\r\n\r\n    return true;\r\n\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\nexport const getUser = async () => {\r\n  const token = Cookies.get('authToken');\r\n  if (!token) return null;\r\n\r\n  try {\r\n    const response = await axios.get(`${apiUrl}/user`, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching user data:', error);\r\n    throw error;\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM;AACN,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC;AAE5B,MAAM,WAAW,OAAO;IAC7B,IAAI;QACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,OAAO,SAAS,CAAC,EAAE,UAAU;YAChE,SAAS;gBACP,gBAAgB;YAClB;YACA,iBAAiB;QACnB;QAEA,OAAO,SAAS,IAAI,EAAE,0BAA0B;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,OAAO,UAAU,OAAO,oBAAoB;QAExD,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;YACnD,OAAO;gBACL,SAAS;gBACT,QAAQ,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;gBACvC,SAAS,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YACtC;QACF;QAEA,OAAO;YACL,SAAS;YACT,QAAQ;gBAAE,SAAS;YAA0C;QAC/D;IACF;AACF;AAGO,MAAM,yBAAyB,OAAO;IAC3C,IAAI;QACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,OAAO,yBAAyB,CAAC,EAAE;QACxE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,QAAQ,MAAM,QAAQ,EAAE,MAAM,UAAU,CAAC;QAAE;IACtD;AACF;AAEO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,OAAO,aAAa,CAAC,EAAE;YACzD,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;QAC5C;IACF;AACF;AAEO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,OAAO,gBAAgB,CAAC,EAAE;QAC/D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE;QAAM;IACjB;AACF;AAEO,MAAM,iBAAiB,OAAO,MAAM,OAAO;IAChD,IAAI;QACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,OAAO,gBAAgB,CAAC,EAAE;YAAE;YAAM;YAAO;QAAK;QACnF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,MAAM,QAAQ,EAAE;YAClB,OAAO;gBAAE,SAAS;gBAAO,SAAS,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI;YAAqC;QACxG;QACA,OAAO;YAAE,SAAS;YAAO,SAAS;QAAgD;IACpF;AACF;AAEO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,oBAAoB,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;QAE5D,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,IAAI,EAAE;YACjD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,GAAG;QAEjB,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,OAAO,eAAe,CAAC,EAAE;YAC5D;YACA,cAAc;QAChB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,SAAS;YAAO,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;QAA2B;IAChG;AACF;AAEO,MAAM,QAAQ,OAAO;IAC1B,IAAI;QACF,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,IAAI,oBAAoB,CAAC,EAAE;YAAE,iBAAiB;QAAK;QAEtE,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,OAAO,MAAM,CAAC,EAAE,aAAa;YAChE,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,iBAAiB;QACnB;QAEA,aAAa,OAAO,CAAC,gBAAgB,KAAK,GAAG,GAAG,QAAQ;QAExD,QAAQ,GAAG,CAAC,yBAAyB,SAAS,IAAI;QAClD,OAAO,SAAS,IAAI;IAEtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB,MAAM,QAAQ;QAEnD,OAAO;YACL,SAAS;YACT,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;YAC1C,QAAQ,MAAM,QAAQ,EAAE,MAAM,UAAU,EAAE;YAC1C,kBACE,MAAM,QAAQ,EAAE,MAAM,SAAS,YAC3B,MAAM,QAAQ,EAAE,MAAM,QACtB;YACN,kBACE,MAAM,QAAQ,EAAE,MAAM,SAAS,aAC3B,MAAM,QAAQ,EAAE,MAAM,QACtB;YACN,aAAa;QACf;IACF;AACF;AAEO,MAAM,SAAS;IACpB,IAAI;QACF,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG;YACvC,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc;YACrD;QACF;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,MAAM,UAAU;IACrB,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAC1B,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI;QACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,OAAO,KAAK,CAAC,EAAE;YACjD,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/redux/authSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport { login } from \"@/utils/auth\";\r\nimport Cookies from \"js-cookie\";\r\nimport toast from \"react-hot-toast\";\r\n\r\nexport const loginUser = createAsyncThunk(\r\n  \"auth/login\",\r\n  async ({ email, password, captchaToken }, { rejectWithValue }) => {\r\n    try {\r\n      const credentials = { email, password, captchaToken };\r\n      const response = await login(credentials);\r\n\r\n      // console.log('slice response',response)\r\n      // console.log('slice response.data',response.data)\r\n\r\n      // if (response?.user) {\r\n      //   localStorage.setItem(\"user\", JSON.stringify(response.user));\r\n      //   Cookies.set(\"authToken\", response.token, { expires: 1 });\r\n      // }\r\n\r\n      // if (response?.captcha_required) {\r\n      //   sessionStorage.setItem(\"captcha_required\", \"true\");\r\n      // } else {\r\n      //   sessionStorage.removeItem(\"captcha_required\");\r\n      // }\r\n      if (response?.data?.user) {\r\n        localStorage.setItem(\"user\", JSON.stringify(response.data.user));\r\n        Cookies.set(\"authToken\", response.data.token);\r\n        if(response?.data?.active_subscription) {\r\n            Cookies.set(\"active_subscription\", response?.data?.active_subscription, { expires: 1 });\r\n        }\r\n        // const subscriptionId = response.data.user.subscription_id;\r\n        // if (subscriptionId) {\r\n        //   Cookies.set(\"subscription_id\", subscriptionId, { expires: 1 });\r\n        // }\r\n      }\r\n\r\n\r\n      if (response?.captcha_required) {\r\n        sessionStorage.setItem(\"captcha_required\", \"true\");\r\n      } else {\r\n        sessionStorage.removeItem(\"captcha_required\");\r\n      }\r\n\r\n      if (!response.success) {\r\n        return rejectWithValue(response);\r\n      }\r\n\r\n      return response;\r\n    } catch (error) {\r\n      console.error(\"Login error message authslice:\", error.response);\r\n\r\n      const errorMessage = error.response?.data?.message || \"An error occurred.\";\r\n      toast.error(errorMessage);\r\n\r\n      return rejectWithValue({\r\n        success: false,\r\n        message: errorMessage,\r\n        errors: error.response?.data?.errors || [],\r\n        captcha_required:\r\n          error.response?.data?.type === 'captcha'\r\n            ? error.response?.data?.state\r\n            : false,\r\n      });\r\n    }\r\n  }\r\n);\r\n\r\n\r\nconst authSlice = createSlice({\r\n  name: \"auth\",\r\n  initialState: {\r\n    user: null,\r\n    token: null,\r\n    loading: false,\r\n    error: null,\r\n  },\r\n  reducers: {\r\n    logoutUser: (state) => {\r\n      state.user = null;\r\n      state.token = null;\r\n      Cookies.remove(\"authToken\");\r\n      localStorage.clear();\r\n      sessionStorage.clear();\r\n    },\r\n    setUser: (state, action) => {\r\n      state.user = action.payload;\r\n    },\r\n  },\r\n  extraReducers: (builder) => {\r\n    builder\r\n      .addCase(loginUser.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(loginUser.fulfilled, (state, action) => {\r\n        state.loading = false;\r\n        state.user = action.payload.user;\r\n        state.token = action.payload.token;\r\n      })\r\n      .addCase(loginUser.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload;\r\n      });\r\n  },\r\n});\r\n\r\nexport const { logoutUser, setUser } = authSlice.actions;\r\nexport const getUser = (state) => state.auth.user;\r\nexport default authSlice.reducer;"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,YAAY,CAAA,GAAA,2LAAA,CAAA,mBAAgB,AAAD,EACtC,cACA,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,EAAE,eAAe,EAAE;IAC3D,IAAI;QACF,MAAM,cAAc;YAAE;YAAO;YAAU;QAAa;QACpD,MAAM,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,QAAK,AAAD,EAAE;QAE7B,yCAAyC;QACzC,mDAAmD;QAEnD,wBAAwB;QACxB,iEAAiE;QACjE,8DAA8D;QAC9D,IAAI;QAEJ,oCAAoC;QACpC,wDAAwD;QACxD,WAAW;QACX,mDAAmD;QACnD,IAAI;QACJ,IAAI,UAAU,MAAM,MAAM;YACxB,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI;YAC9D,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,aAAa,SAAS,IAAI,CAAC,KAAK;YAC5C,IAAG,UAAU,MAAM,qBAAqB;gBACpC,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,uBAAuB,UAAU,MAAM,qBAAqB;oBAAE,SAAS;gBAAE;YACzF;QACA,6DAA6D;QAC7D,wBAAwB;QACxB,oEAAoE;QACpE,IAAI;QACN;QAGA,IAAI,UAAU,kBAAkB;YAC9B,eAAe,OAAO,CAAC,oBAAoB;QAC7C,OAAO;YACL,eAAe,UAAU,CAAC;QAC5B;QAEA,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,OAAO,gBAAgB;QACzB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC,MAAM,QAAQ;QAE9D,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;QACtD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAEZ,OAAO,gBAAgB;YACrB,SAAS;YACT,SAAS;YACT,QAAQ,MAAM,QAAQ,EAAE,MAAM,UAAU,EAAE;YAC1C,kBACE,MAAM,QAAQ,EAAE,MAAM,SAAS,YAC3B,MAAM,QAAQ,EAAE,MAAM,QACtB;QACR;IACF;AACF;AAIF,MAAM,YAAY,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN,cAAc;QACZ,MAAM;QACN,OAAO;QACP,SAAS;QACT,OAAO;IACT;IACA,UAAU;QACR,YAAY,CAAC;YACX,MAAM,IAAI,GAAG;YACb,MAAM,KAAK,GAAG;YACd,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;YACf,aAAa,KAAK;YAClB,eAAe,KAAK;QACtB;QACA,SAAS,CAAC,OAAO;YACf,MAAM,IAAI,GAAG,OAAO,OAAO;QAC7B;IACF;IACA,eAAe,CAAC;QACd,QACG,OAAO,CAAC,UAAU,OAAO,EAAE,CAAC;YAC3B,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,UAAU,SAAS,EAAE,CAAC,OAAO;YACpC,MAAM,OAAO,GAAG;YAChB,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI;YAChC,MAAM,KAAK,GAAG,OAAO,OAAO,CAAC,KAAK;QACpC,GACC,OAAO,CAAC,UAAU,QAAQ,EAAE,CAAC,OAAO;YACnC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;IACJ;AACF;AAEO,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,UAAU,OAAO;AACjD,MAAM,UAAU,CAAC,QAAU,MAAM,IAAI,CAAC,IAAI;uCAClC,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 516, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/redux/store/authStore.js"], "sourcesContent": ["// src/redux/store/authStore.js\r\n\r\nimport { configureStore } from '@reduxjs/toolkit';\r\nimport authReducer from '../authSlice';\r\n\r\nconst authStore = configureStore({\r\n  reducer: {\r\n    auth: authReducer,\r\n  },\r\n});\r\n\r\nexport default authStore;\r\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;AAE/B;AACA;;;AAEA,MAAM,YAAY,CAAA,GAAA,2LAAA,CAAA,iBAAc,AAAD,EAAE;IAC/B,SAAS;QACP,MAAM,yHAAA,CAAA,UAAW;IACnB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/providers/Providers.js"], "sourcesContent": ["\"use client\"; \r\n\r\nimport { Provider } from 'react-redux';\r\nimport authStore from \"@/redux/store/authStore\";\r\n\r\nexport default function Providers({ children }) {\r\n  return <Provider store={authStore}>{children}</Provider>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS,UAAU,EAAE,QAAQ,EAAE;IAC5C,qBAAO,8OAAC,yJAAA,CAAA,WAAQ;QAAC,OAAO,kIAAA,CAAA,UAAS;kBAAG;;;;;;AACtC", "debugId": null}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/redux/metaSlice.js"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\n\r\nconst initialState = {\r\n  title: \"Default Title\",\r\n  description: \"Default description\",\r\n  keywords: \"default, keywords\",\r\n};\r\n\r\nconst metaSlice = createSlice({\r\n  name: \"meta\",\r\n  initialState,\r\n  reducers: {\r\n    setMeta: (state, action) => {\r\n      return { ...state, ...action.payload };\r\n    },\r\n  },\r\n});\r\n\r\nexport const { setMeta } = metaSlice.actions;\r\nexport default metaSlice.reducer;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,eAAe;IACnB,OAAO;IACP,aAAa;IACb,UAAU;AACZ;AAEA,MAAM,YAAY,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,SAAS,CAAC,OAAO;YACf,OAAO;gBAAE,GAAG,KAAK;gBAAE,GAAG,OAAO,OAAO;YAAC;QACvC;IACF;AACF;AAEO,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,OAAO;uCAC7B,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/redux/store/metaStore.js"], "sourcesContent": ["import { configureStore } from \"@reduxjs/toolkit\";\r\nimport metaReducer from \"../metaSlice\";\r\n\r\nconst store = configureStore({\r\n  reducer: {\r\n    meta: metaReducer,\r\n  },\r\n});\r\n\r\nexport default store;\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,QAAQ,CAAA,GAAA,2LAAA,CAAA,iBAAc,AAAD,EAAE;IAC3B,SAAS;QACP,MAAM,yHAAA,CAAA,UAAW;IACnB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 606, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/providers/MetaProvider.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Provider } from 'react-redux';\r\nimport store from \"@/redux/store/metaStore\";\r\n\r\n  export default function MetaProvider({ children }) {\r\n  return <Provider store={store}>{children}</Provider>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKiB,SAAS,aAAa,EAAE,QAAQ,EAAE;IACjD,qBAAO,8OAAC,yJAAA,CAAA,WAAQ;QAAC,OAAO,kIAAA,CAAA,UAAK;kBAAG;;;;;;AAClC", "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/lib/i18n.js"], "sourcesContent": ["\r\n\r\nimport i18n from 'i18next';\r\nimport { initReactI18next } from 'react-i18next';\r\nimport Backend from 'i18next-http-backend';\r\nimport LanguageDetector from 'i18next-browser-languagedetector';\r\n\r\ni18n\r\n  .use(Backend) \r\n  .use(LanguageDetector) \r\n  .use(initReactI18next) \r\n  .init({\r\n    fallbackLng: 'en', \r\n    debug: false, \r\n    supportedLngs: ['en', 'fr'], \r\n    backend: {\r\n      loadPath: '/locales/{{lng}}.json', \r\n    },\r\n    interpolation: {\r\n      escapeValue: false, \r\n    },\r\n  });\r\n\r\nexport default i18n;\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAAA;AACA;AACA;;;;;AAEA,iJAAA,CAAA,UAAI,CACD,GAAG,CAAC,0JAAA,CAAA,UAAO,EACX,GAAG,CAAC,uMAAA,CAAA,UAAgB,EACpB,GAAG,CAAC,kKAAA,CAAA,mBAAgB,EACpB,IAAI,CAAC;IACJ,aAAa;IACb,OAAO;IACP,eAAe;QAAC;QAAM;KAAK;IAC3B,SAAS;QACP,UAAU;IACZ;IACA,eAAe;QACb,aAAa;IACf;AACF;uCAEa,iJAAA,CAAA,UAAI", "debugId": null}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/providers/I18nProvider.js"], "sourcesContent": ["\r\n'use client'; \r\n\r\nimport { I18nextProvider } from 'react-i18next';\r\nimport i18n from '@/lib/i18n';\r\n\r\nconst I18nProvider = ({ children }) => {\r\n  return <I18nextProvider i18n={i18n}>{children}</I18nextProvider>;\r\n};\r\n\r\nexport default I18nProvider;\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAHA;;;;AAKA,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAE;IAChC,qBAAO,8OAAC,iKAAA,CAAA,kBAAe;QAAC,MAAM,kHAAA,CAAA,UAAI;kBAAG;;;;;;AACvC;uCAEe", "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 699, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/context/LanguageContext.js"], "sourcesContent": ["\"use client\"; // Required for Next.js client-side state\r\n\r\nimport { createContext, useContext, useState } from \"react\";\r\nimport i18next from \"i18next\";\r\n\r\n// Create Context\r\nconst LanguageContext = createContext(null);\r\n\r\n// Provider Component\r\nexport const LanguageProvider = ({ children }) => {\r\n  const [language, setLanguage] = useState(\"en\");\r\n\r\n  const changeLanguage = (lang) => {\r\n    setLanguage(lang); // ✅ This updates state\r\n    i18next.changeLanguage(lang);\r\n  };\r\n\r\n  return (\r\n    <LanguageContext.Provider value={{ language, changeLanguage }}>\r\n      {children}\r\n    </LanguageContext.Provider>\r\n  );\r\n};\r\n\r\n// Custom Hook\r\nexport const useLanguage = () => {\r\n  const context = useContext(LanguageContext);\r\n  if (!context) {\r\n    throw new Error(\"useLanguage must be used within a LanguageProvider\");\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA,cAAc,yCAAyC;;;;AAKvD,iBAAiB;AACjB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AAG/B,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,iBAAiB,CAAC;QACtB,YAAY,OAAO,uBAAuB;QAC1C,iJAAA,CAAA,UAAO,CAAC,cAAc,CAAC;IACzB;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;YAAU;QAAe;kBACzD;;;;;;AAGP;AAGO,MAAM,cAAc;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/lib/useTranslation.js"], "sourcesContent": ["\"use client\"; // Since hooks use client-side state\r\n\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { useLanguage } from \"@/context/LanguageContext\";\r\n\r\nexport const t = (key) => {\r\n  const { t } = useTranslation();\r\n  return t(key) || key; // Return key if translation is missing\r\n};\r\n\r\n// Hook to get and change language\r\nexport const useSetLanguage = () => {\r\n  return useLanguage(); // Returns { language, changeLanguage }\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAHA,cAAc,oCAAoC;;;AAK3C,MAAM,IAAI,CAAC;IAChB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD;IAC3B,OAAO,EAAE,QAAQ,KAAK,uCAAuC;AAC/D;AAGO,MAAM,iBAAiB;IAC5B,OAAO,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,KAAK,uCAAuC;AAC/D", "debugId": null}}, {"offset": {"line": 760, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/ClientSideCanonicalTag.js"], "sourcesContent": ["'use client';\r\nimport { usePathname, useSearchParams } from 'next/navigation';\r\nimport Cookies from 'js-cookie';\r\n\r\n\r\nconst ClientSideCanonicalTag = () => {\r\n  \r\n  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;\r\n  const pathname = usePathname();\r\n  const searchParams = useSearchParams();\r\n\r\n  const keyValue = searchParams?.get('key');\r\n  const isSearchPage = keyValue !== null && keyValue.trim() !== '';\r\n\r\n  const hasKeyParam = keyValue !== null && keyValue.trim() !== '';\r\n\r\n\r\n  // const baseUrl = environment === 'dev' ? 'https://dev.tradereply.com' : 'https://www.tradereply.com';\r\n\r\n  // const pageParam = searchParams?.get('page');\r\n  // const currentPage = pageParam ? parseInt(pageParam, 10) : 1;\r\n\r\n  // const canonicalUrl = `${baseUrl}${pathname}`\r\n\r\n  // const prevPage = currentPage > 1 ? `${baseUrl}${pathname}?page=${currentPage - 1}` : null;\r\n  // const nextPage = `${baseUrl}${pathname}?page=${currentPage + 1}`;\r\n\r\n  const baseUrl =\r\n    environment === 'dev'\r\n      ? 'https://dev.tradereply.com'\r\n      : 'https://www.tradereply.com';\r\n\r\n\r\n  // Extract current page from pathname (e.g. /education/page/3)\r\n  const pathMatch = pathname?.match(/\\/page\\/(\\d+)$/);\r\n  const currentPage = pathMatch ? parseInt(pathMatch[1], 10) : 1;\r\n\r\n  // Base path without /page/X\r\n  const basePath = pathname.replace(/\\/page\\/\\d+$/, '');\r\n\r\n  // Canonical\r\n  const canonicalUrl =\r\n    currentPage === 1\r\n      ? `${baseUrl}${basePath}`\r\n      : `${baseUrl}${basePath}/page/${currentPage}`;\r\n\r\n  // Prev\r\n  const prevPage =\r\n    currentPage > 1\r\n      ? currentPage === 2\r\n        ? `${baseUrl}${basePath}`\r\n        : `${baseUrl}${basePath}/page/${currentPage - 1}`\r\n      : null;\r\n\r\n  // Next (you can conditionally hide this if you know totalPages)\r\n  const nextPage = `${baseUrl}${basePath}/page/${currentPage + 1}`;\r\n  \r\n\r\n\r\n  const isCategoryPage = pathname?.startsWith('/category');\r\n \r\n  return (\r\n    <>\r\n      {/* {hasKeyParam ? (\r\n        <meta name=\"robots\" content=\"noindex\" />\r\n      ) : (\r\n        <>\r\n          {!isCategoryPage && <link rel=\"canonical\" href={canonicalUrl} />}\r\n          <meta property=\"og:url\" \r\n          content={canonicalUrl}\r\n           />\r\n          {prevPage && <link rel=\"prev\" href={prevPage} />}\r\n          <link rel=\"next\" href={nextPage} />\r\n        </>\r\n      )} */}\r\n      {isSearchPage ? (\r\n        <meta name=\"robots\" content=\"noindex\" />\r\n      ) : (\r\n        <>\r\n          {/* <link rel=\"canonical\" href={canonicalUrl} /> */}\r\n          <meta property=\"og:url\" content={canonicalUrl} />\r\n          {prevPage && <link rel=\"prev\" href={prevPage} />}\r\n          {/* <link rel=\"next\" href={nextPage} /> */}\r\n        </>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ClientSideCanonicalTag;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAKA,MAAM,yBAAyB;IAE7B,MAAM;IACN,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,MAAM,WAAW,cAAc,IAAI;IACnC,MAAM,eAAe,aAAa,QAAQ,SAAS,IAAI,OAAO;IAE9D,MAAM,cAAc,aAAa,QAAQ,SAAS,IAAI,OAAO;IAG7D,uGAAuG;IAEvG,+CAA+C;IAC/C,+DAA+D;IAE/D,+CAA+C;IAE/C,6FAA6F;IAC7F,oEAAoE;IAEpE,MAAM,UACJ,uCACI;IAIN,8DAA8D;IAC9D,MAAM,YAAY,UAAU,MAAM;IAClC,MAAM,cAAc,YAAY,SAAS,SAAS,CAAC,EAAE,EAAE,MAAM;IAE7D,4BAA4B;IAC5B,MAAM,WAAW,SAAS,OAAO,CAAC,gBAAgB;IAElD,YAAY;IACZ,MAAM,eACJ,gBAAgB,IACZ,GAAG,UAAU,UAAU,GACvB,GAAG,UAAU,SAAS,MAAM,EAAE,aAAa;IAEjD,OAAO;IACP,MAAM,WACJ,cAAc,IACV,gBAAgB,IACd,GAAG,UAAU,UAAU,GACvB,GAAG,UAAU,SAAS,MAAM,EAAE,cAAc,GAAG,GACjD;IAEN,gEAAgE;IAChE,MAAM,WAAW,GAAG,UAAU,SAAS,MAAM,EAAE,cAAc,GAAG;IAIhE,MAAM,iBAAiB,UAAU,WAAW;IAE5C,qBACE;kBAaG,6BACC,8OAAC;YAAK,MAAK;YAAS,SAAQ;;;;;iCAE5B;;8BAEE,8OAAC;oBAAK,UAAS;oBAAS,SAAS;;;;;;gBAChC,0BAAY,8OAAC;oBAAK,KAAI;oBAAO,MAAM;;;;;;;;;AAM9C;uCAEe", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 839, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/axiosInstance.js"], "sourcesContent": ["import axios from 'axios';\r\nimport Cookies from 'js-cookie';\r\nimport toast from 'react-hot-toast';\r\n\r\nconst axiosInstance = axios.create({\r\n  baseURL: `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/`,\r\n  withCredentials: true,\r\n  headers: {\r\n    \"Accept\": \"application/json\"\r\n  },\r\n});\r\n\r\n// Flag to prevent multiple simultaneous logout redirects\r\nlet isRedirecting = false;\r\n\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    const token = Cookies.get(\"authToken\"); // Fetch latest token\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => Promise.reject(error)\r\n);\r\n\r\n// Response Interceptor: Handle Unauthorized Errors\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.response) {\r\n      const { status } = error.response;\r\n\r\n      if (status === 401) {\r\n        handleUnauthorizedAccess();\r\n      } else if (status === 404) {\r\n        console.error('Resource not found!');\r\n      } else if (status >= 500) {\r\n        console.error('Server error! Please try again later.');\r\n      }\r\n    } else {\r\n      console.error('Network error or request timeout.');\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n/**\r\n * Handle unauthorized access with proper cleanup and redirect\r\n */\r\nfunction handleUnauthorizedAccess() {\r\n  // Prevent multiple simultaneous redirects\r\n  if (isRedirecting) {\r\n    return;\r\n  }\r\n\r\n  isRedirecting = true;\r\n\r\n  console.log('Unauthorized access detected. Logging out...');\r\n\r\n  // Clear all authentication data\r\n  Cookies.remove(\"authToken\");\r\n  localStorage.removeItem(\"user\");\r\n  localStorage.removeItem(\"lastActivity\");\r\n  localStorage.setItem(\"loggedOut\", Date.now());\r\n  sessionStorage.clear();\r\n\r\n  // Show user notification\r\n  // toast.error('Your session has expired. Please log in again.', {\r\n  //   duration: 4000,\r\n  //   position: 'top-center',\r\n  // });\r\n\r\n  // Use window.location for redirect (most reliable method)\r\n  if (typeof window !== 'undefined') {\r\n    // Check if we're already on login page to prevent redirect loop\r\n    if (window.location.pathname !== '/login') {\r\n      window.location.href = '/login';\r\n    }\r\n  }\r\n\r\n  // Reset redirect flag after a delay\r\n  setTimeout(() => {\r\n    isRedirecting = false;\r\n  }, 1000);\r\n}\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,gBAAgB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACjC,SAAS,6DAAwC,QAAQ,CAAC;IAC1D,iBAAiB;IACjB,SAAS;QACP,UAAU;IACZ;AACF;AAEA,yDAAyD;AACzD,IAAI,gBAAgB;AAEpB,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;IACC,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc,qBAAqB;IAC7D,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;AAG5B,mDAAmD;AACnD,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;QAEjC,IAAI,WAAW,KAAK;YAClB;QACF,OAAO,IAAI,WAAW,KAAK;YACzB,QAAQ,KAAK,CAAC;QAChB,OAAO,IAAI,UAAU,KAAK;YACxB,QAAQ,KAAK,CAAC;QAChB;IACF,OAAO;QACL,QAAQ,KAAK,CAAC;IAChB;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF;;CAEC,GACD,SAAS;IACP,0CAA0C;IAC1C,IAAI,eAAe;QACjB;IACF;IAEA,gBAAgB;IAEhB,QAAQ,GAAG,CAAC;IAEZ,gCAAgC;IAChC,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IACf,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;IACxB,aAAa,OAAO,CAAC,aAAa,KAAK,GAAG;IAC1C,eAAe,KAAK;IAEpB,yBAAyB;IACzB,kEAAkE;IAClE,oBAAoB;IACpB,4BAA4B;IAC5B,MAAM;IAEN,0DAA0D;IAC1D,uCAAmC;;IAKnC;IAEA,oCAAoC;IACpC,WAAW;QACT,gBAAgB;IAClB,GAAG;AACL;uCAEe", "debugId": null}}, {"offset": {"line": 910, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 916, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/apiUtils.js"], "sourcesContent": ["import axiosInstance from './axiosInstance';\r\nimport axios from 'axios';\r\n\r\n// GET Request Utility\r\nexport const get = async (url, params = {}) => {\r\n  try {\r\n    const response = await axiosInstance.get(url, { params });\r\n    return response.data;\r\n  } catch (error) {\r\n    if (error.name === 'AbortError' || axios.isCancel(error)) { // Handle both AbortError and Axios cancel\r\n      console.log('Request aborted:', url); // Optional quiet log\r\n      return null; // Or throw if needed\r\n    }\r\n    console.error('Error with GET request:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getBrokers = async (url, params = {}) => {\r\n  try {\r\n    const response = await axiosInstance.get(url, { params });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error with GET request:', error);\r\n    throw error; // Re-throw for further handling\r\n  }\r\n};\r\n\r\n// POST Request Utility\r\nexport const post = async (url, data) => {\r\n  try {\r\n    const response = await axiosInstance.post(url, data);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error with POST request:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// PUT Request Utility\r\nexport const put = async (url, data) => {\r\n  try {\r\n    const response = await axiosInstance.put(url, data);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error with PUT request:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// DELETE Request Utility\r\nexport const deleteRequest = async (url) => {\r\n  try {\r\n    const response = await axiosInstance.delete(url);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error with DELETE request:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Username management utilities\r\nexport const updateUsername = async (username) => {\r\n  try {\r\n    const response = await axiosInstance.post('/username/update', { username });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating username:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const checkUsernameAvailability = async (username) => {\r\n  try {\r\n    const response = await axiosInstance.post('/username/check-availability', { username });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error checking username availability:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Two-Factor Authentication utilities\r\nexport const get2FAStatus = async () => {\r\n  try {\r\n    const response = await axiosInstance.get('/2fa/status');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching 2FA status:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const enable2FA = async () => {\r\n  try {\r\n    const response = await axiosInstance.post('/2fa/enable');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error enabling 2FA:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const disable2FA = async () => {\r\n  try {\r\n    const response = await axiosInstance.post('/2fa/disable');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error disabling 2FA:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const generateRestoreCode = async () => {\r\n  try {\r\n    const response = await axiosInstance.post('/2fa/generate-restore-code');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error generating restore code:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const update2FAAlwaysRequired = async (alwaysRequired) => {\r\n  try {\r\n    const response = await axiosInstance.post('/2fa/update-always-required', {\r\n      always_required: alwaysRequired\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating 2FA always required setting:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Secret Questions utilities\r\nexport const getSecretQuestions = async () => {\r\n  try {\r\n    const response = await axiosInstance.get('/account/secret-questions');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching secret questions:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const saveSecretQuestions = async (questions) => {\r\n  try {\r\n    const response = await axiosInstance.post('/account/secret-questions', { questions });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error saving secret questions:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const updateSecretQuestions = async (questions) => {\r\n  try {\r\n    const response = await axiosInstance.put('/account/secret-questions', { questions });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating secret questions:', error);\r\n    throw error;\r\n  }\r\n};"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,MAAM,MAAM,OAAO,KAAK,SAAS,CAAC,CAAC;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC,KAAK;YAAE;QAAO;QACvD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,MAAM,IAAI,KAAK,gBAAgB,qIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,QAAQ;YACxD,QAAQ,GAAG,CAAC,oBAAoB,MAAM,qBAAqB;YAC3D,OAAO,MAAM,qBAAqB;QACpC;QACA,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,MAAM,aAAa,OAAO,KAAK,SAAS,CAAC,CAAC;IAC/C,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC,KAAK;YAAE;QAAO;QACvD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM,OAAO,gCAAgC;IAC/C;AACF;AAGO,MAAM,OAAO,OAAO,KAAK;IAC9B,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,KAAK;QAC/C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAGO,MAAM,MAAM,OAAO,KAAK;IAC7B,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC,KAAK;QAC9C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAGO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,MAAM,CAAC;QAC5C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAGO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,oBAAoB;YAAE;QAAS;QACzE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,MAAM,4BAA4B,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,gCAAgC;YAAE;QAAS;QACrF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM;IACR;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAEO,MAAM,YAAY;IACvB,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC;QAC1C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF;AAEO,MAAM,aAAa;IACxB,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC;QAC1C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC;QAC1C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,0BAA0B,OAAO;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,+BAA+B;YACvE,iBAAiB;QACnB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,6BAA6B;YAAE;QAAU;QACnF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,OAAO;IAC1C,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC,6BAA6B;YAAE;QAAU;QAClF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Hooks/useAuthHeartbeat.js"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useRef } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport Cookies from 'js-cookie';\r\nimport { get } from '@/utils/apiUtils';\r\n\r\n/**\r\n * Custom hook to monitor authentication status with periodic heartbeat\r\n * Automatically detects when user is logged out from other devices\r\n */\r\nexport default function useAuthHeartbeat() {\r\n  const router = useRouter();\r\n  const intervalRef = useRef(null);\r\n  const isActiveRef = useRef(true);\r\n  const lastHeartbeatRef = useRef(Date.now());\r\n\r\n  // Configuration\r\n  const HEARTBEAT_INTERVAL = 1000; // 1 second for near-instant detection\r\n  const MAX_RETRY_ATTEMPTS = 3;\r\n  const RETRY_DELAY = 5000; // 5 seconds\r\n\r\n  /**\r\n   * Perform heartbeat check\r\n   */\r\n  const performHeartbeat = async () => {\r\n    try {\r\n      // Only perform heartbeat if user has auth token\r\n      const token = Cookies.get(\"authToken\");\r\n      if (!token) {\r\n        stopHeartbeat();\r\n        return;\r\n      }\r\n\r\n      // Only perform heartbeat if tab is active and user is active\r\n      if (!isActiveRef.current || document.hidden) {\r\n        return;\r\n      }\r\n\r\n      // Make lightweight API call to verify token\r\n      await get('auth/heartbeat');\r\n\r\n      // Update last successful heartbeat\r\n      lastHeartbeatRef.current = Date.now();\r\n\r\n    } catch (error) {\r\n      // 401 errors are handled by axios interceptor\r\n      // Other errors we can log but don't need to handle specially\r\n      if (error?.response?.status !== 401) {\r\n        console.warn('Heartbeat failed:', error.message);\r\n      }\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Start heartbeat monitoring\r\n   */\r\n  const startHeartbeat = () => {\r\n    // Don't start if already running\r\n    if (intervalRef.current) {\r\n      return;\r\n    }\r\n\r\n    // Only start if user has auth token\r\n    const token = Cookies.get(\"authToken\");\r\n    if (!token) {\r\n      return;\r\n    }\r\n\r\n    // Heartbeat monitoring started\r\n\r\n    // Perform initial heartbeat\r\n    performHeartbeat();\r\n\r\n    // Set up interval\r\n    intervalRef.current = setInterval(performHeartbeat, HEARTBEAT_INTERVAL);\r\n  };\r\n\r\n  /**\r\n   * Stop heartbeat monitoring\r\n   */\r\n  const stopHeartbeat = () => {\r\n    if (intervalRef.current) {\r\n      clearInterval(intervalRef.current);\r\n      intervalRef.current = null;\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handle visibility change (tab focus/blur)\r\n   */\r\n  const handleVisibilityChange = () => {\r\n    if (document.hidden) {\r\n      // Tab is hidden, reduce activity\r\n      isActiveRef.current = false;\r\n    } else {\r\n      // Tab is visible, resume activity\r\n      isActiveRef.current = true;\r\n      \r\n      // If it's been a while since last heartbeat, perform one immediately\r\n      const timeSinceLastHeartbeat = Date.now() - lastHeartbeatRef.current;\r\n      if (timeSinceLastHeartbeat > HEARTBEAT_INTERVAL) {\r\n        performHeartbeat();\r\n      }\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handle user activity (mouse, keyboard, etc.)\r\n   * Trigger immediate heartbeat on user activity for faster detection\r\n   */\r\n  const handleUserActivity = () => {\r\n    isActiveRef.current = true;\r\n    localStorage.setItem(\"lastActivity\", Date.now().toString());\r\n\r\n    // Trigger immediate heartbeat on user activity for faster logout detection\r\n    const timeSinceLastHeartbeat = Date.now() - lastHeartbeatRef.current;\r\n    if (timeSinceLastHeartbeat > 500) { // Throttle to max 2 per second\r\n      performHeartbeat();\r\n    }\r\n  };\r\n\r\n  // Set up effect\r\n  useEffect(() => {\r\n    // Start heartbeat monitoring\r\n    startHeartbeat();\r\n\r\n    // Listen for visibility changes\r\n    document.addEventListener('visibilitychange', handleVisibilityChange);\r\n\r\n    // Listen for window focus (when user switches back to tab)\r\n    window.addEventListener('focus', performHeartbeat);\r\n\r\n    // Listen for user activity (more events for faster detection)\r\n    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];\r\n    activityEvents.forEach(event => {\r\n      document.addEventListener(event, handleUserActivity, { passive: true });\r\n    });\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      stopHeartbeat();\r\n      document.removeEventListener('visibilitychange', handleVisibilityChange);\r\n      window.removeEventListener('focus', performHeartbeat);\r\n\r\n      activityEvents.forEach(event => {\r\n        document.removeEventListener(event, handleUserActivity);\r\n      });\r\n    };\r\n  }, []);\r\n\r\n  // Return control functions (optional, for manual control)\r\n  return {\r\n    startHeartbeat,\r\n    stopHeartbeat,\r\n    performHeartbeat\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AALA;;;;;AAWe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,KAAK,GAAG;IAExC,gBAAgB;IAChB,MAAM,qBAAqB,MAAM,sCAAsC;IACvE,MAAM,qBAAqB;IAC3B,MAAM,cAAc,MAAM,YAAY;IAEtC;;GAEC,GACD,MAAM,mBAAmB;QACvB,IAAI;YACF,gDAAgD;YAChD,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAC1B,IAAI,CAAC,OAAO;gBACV;gBACA;YACF;YAEA,6DAA6D;YAC7D,IAAI,CAAC,YAAY,OAAO,IAAI,SAAS,MAAM,EAAE;gBAC3C;YACF;YAEA,4CAA4C;YAC5C,MAAM,CAAA,GAAA,wHAAA,CAAA,MAAG,AAAD,EAAE;YAEV,mCAAmC;YACnC,iBAAiB,OAAO,GAAG,KAAK,GAAG;QAErC,EAAE,OAAO,OAAO;YACd,8CAA8C;YAC9C,6DAA6D;YAC7D,IAAI,OAAO,UAAU,WAAW,KAAK;gBACnC,QAAQ,IAAI,CAAC,qBAAqB,MAAM,OAAO;YACjD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB;QACrB,iCAAiC;QACjC,IAAI,YAAY,OAAO,EAAE;YACvB;QACF;QAEA,oCAAoC;QACpC,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAC1B,IAAI,CAAC,OAAO;YACV;QACF;QAEA,+BAA+B;QAE/B,4BAA4B;QAC5B;QAEA,kBAAkB;QAClB,YAAY,OAAO,GAAG,YAAY,kBAAkB;IACtD;IAEA;;GAEC,GACD,MAAM,gBAAgB;QACpB,IAAI,YAAY,OAAO,EAAE;YACvB,cAAc,YAAY,OAAO;YACjC,YAAY,OAAO,GAAG;QACxB;IACF;IAEA;;GAEC,GACD,MAAM,yBAAyB;QAC7B,IAAI,SAAS,MAAM,EAAE;YACnB,iCAAiC;YACjC,YAAY,OAAO,GAAG;QACxB,OAAO;YACL,kCAAkC;YAClC,YAAY,OAAO,GAAG;YAEtB,qEAAqE;YACrE,MAAM,yBAAyB,KAAK,GAAG,KAAK,iBAAiB,OAAO;YACpE,IAAI,yBAAyB,oBAAoB;gBAC/C;YACF;QACF;IACF;IAEA;;;GAGC,GACD,MAAM,qBAAqB;QACzB,YAAY,OAAO,GAAG;QACtB,aAAa,OAAO,CAAC,gBAAgB,KAAK,GAAG,GAAG,QAAQ;QAExD,2EAA2E;QAC3E,MAAM,yBAAyB,KAAK,GAAG,KAAK,iBAAiB,OAAO;QACpE,IAAI,yBAAyB,KAAK;YAChC;QACF;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B;QAEA,gCAAgC;QAChC,SAAS,gBAAgB,CAAC,oBAAoB;QAE9C,2DAA2D;QAC3D,OAAO,gBAAgB,CAAC,SAAS;QAEjC,8DAA8D;QAC9D,MAAM,iBAAiB;YAAC;YAAa;YAAa;YAAY;YAAU;YAAc;SAAQ;QAC9F,eAAe,OAAO,CAAC,CAAA;YACrB,SAAS,gBAAgB,CAAC,OAAO,oBAAoB;gBAAE,SAAS;YAAK;QACvE;QAEA,qBAAqB;QACrB,OAAO;YACL;YACA,SAAS,mBAAmB,CAAC,oBAAoB;YACjD,OAAO,mBAAmB,CAAC,SAAS;YAEpC,eAAe,OAAO,CAAC,CAAA;gBACrB,SAAS,mBAAmB,CAAC,OAAO;YACtC;QACF;IACF,GAAG,EAAE;IAEL,0DAA0D;IAC1D,OAAO;QACL;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1236, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1242, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/auth/AuthHeartbeatProvider.js"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport { usePathname } from 'next/navigation';\r\nimport Cookies from 'js-cookie';\r\nimport useAuthHeartbeat from '@/Hooks/useAuthHeartbeat';\r\n\r\n/**\r\n * Provider component that handles authentication heartbeat monitoring\r\n * Only activates on authenticated pages (not login, signup, etc.)\r\n */\r\nexport default function AuthHeartbeatProvider({ children }) {\r\n  const pathname = usePathname();\r\n  const { startHeartbeat, stopHeartbeat } = useAuthHeartbeat();\r\n\r\n  // Define routes that don't need heartbeat monitoring (public routes)\r\n  const publicRoutes = [\r\n    '/login',\r\n    '/signup',\r\n    '/forget',\r\n    '/reset-password',\r\n    '/verify-email',\r\n    '/locate-account',\r\n    '/auth',\r\n    '/',\r\n    '/blog',\r\n    '/education',\r\n    '/about',\r\n    '/contact',\r\n    '/privacy',\r\n    '/terms',\r\n    '/pricing'\r\n  ];\r\n\r\n  // Define routes that definitely need heartbeat monitoring (authenticated routes)\r\n  const authenticatedRoutes = [\r\n    '/dashboard',\r\n    '/account',\r\n    '/create-username',\r\n    '/change-password',\r\n    '/security-check'\r\n  ];\r\n\r\n  // Check if current route needs authentication monitoring\r\n  const isAuthenticatedRoute = () => {\r\n    // First check if it's explicitly an authenticated route\r\n    if (authenticatedRoutes.some(route => pathname.startsWith(route))) {\r\n      return true;\r\n    }\r\n\r\n    // If it's a public route, no monitoring needed\r\n    if (publicRoutes.some(route => pathname.startsWith(route))) {\r\n      return false;\r\n    }\r\n\r\n    // For other routes, check if user has auth token\r\n    const token = Cookies.get(\"authToken\");\r\n    return !!token;\r\n  };\r\n\r\n  useEffect(() => {\r\n    const isAuth = isAuthenticatedRoute();\r\n\r\n    if (isAuth) {\r\n      // Start heartbeat monitoring for authenticated routes\r\n      startHeartbeat();\r\n    } else {\r\n      // Stop heartbeat monitoring for public routes\r\n      stopHeartbeat();\r\n    }\r\n\r\n    // Cleanup on route change or unmount\r\n    return () => {\r\n      // Don't stop heartbeat on cleanup unless we're going to a public route\r\n      // This prevents stopping heartbeat when navigating between authenticated pages\r\n    };\r\n  }, [pathname, startHeartbeat, stopHeartbeat]);\r\n\r\n  return children;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AALA;;;;;AAWe,SAAS,sBAAsB,EAAE,QAAQ,EAAE;IACxD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,UAAgB,AAAD;IAEzD,qEAAqE;IACrE,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,iFAAiF;IACjF,MAAM,sBAAsB;QAC1B;QACA;QACA;QACA;QACA;KACD;IAED,yDAAyD;IACzD,MAAM,uBAAuB;QAC3B,wDAAwD;QACxD,IAAI,oBAAoB,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC,SAAS;YACjE,OAAO;QACT;QAEA,+CAA+C;QAC/C,IAAI,aAAa,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC,SAAS;YAC1D,OAAO;QACT;QAEA,iDAAiD;QACjD,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAC1B,OAAO,CAAC,CAAC;IACX;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS;QAEf,IAAI,QAAQ;YACV,sDAAsD;YACtD;QACF,OAAO;YACL,8CAA8C;YAC9C;QACF;QAEA,qCAAqC;QACrC,OAAO;QACL,uEAAuE;QACvE,+EAA+E;QACjF;IACF,GAAG;QAAC;QAAU;QAAgB;KAAc;IAE5C,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1318, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}