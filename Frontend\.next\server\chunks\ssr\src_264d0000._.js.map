{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Header.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Header.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Header.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Header.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Header.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Header.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Footer.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Footer.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Footer.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Footer.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Footer.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Footer.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Layouts/HomeLayout.js"], "sourcesContent": ["// 'use client';\r\n\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { Fragment } from \"react\";\r\nimport Header from \"@/Components/UI/Header\";\r\nimport Footer from \"@/Components/UI/Footer\";\r\nimport { LanguageProvider } from \"@/context/LanguageContext\";\r\n\r\nconst HomeLayout = ({ children }) => {\r\n  return (\r\n    <Fragment>\r\n      <LanguageProvider>\r\n        <Header />\r\n        {/* Wrap main content in <main> for SEO & accessibility */}\r\n        <main className=\"main-content\">{children}</main>\r\n        <Footer />\r\n      </LanguageProvider>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default HomeLayout;\r\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;AAGhB;AACA;AACA;AACA;;;;;;;AAEA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAE;IAC9B,qBACE,8OAAC,qMAAA,CAAA,WAAQ;kBACP,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;;8BACf,8OAAC,iIAAA,CAAA,UAAM;;;;;8BAEP,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;8BAChC,8OAAC,iIAAA,CAAA,UAAM;;;;;;;;;;;;;;;;AAIf;uCAEe", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/marketplace/%5Bdetail%5D/layout.js"], "sourcesContent": ["// src/app/(Home)/marketplace/[detail]/layout.js\r\nimport HomeLayout from \"@/Layouts/HomeLayout\";\r\nimport RootLayout from \"@/app/layout\";\r\nimport { generateProductSchema } from \"@/Seo/Schema/JsonLdSchema\";\r\n\r\nexport const dynamic = \"force-dynamic\"; // ensure fresh fetch each request\r\n\r\nexport default async function MarketplaceDetailLayout({ children, params }) {\r\n  const { detail } = params;\r\n\r\n  // 1) Fetch your product data (swap in your real API)\r\n  const res = await fetch(\r\n    `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/marketplace/${detail}`\r\n  );\r\n  if (!res.ok) throw new Error(`Failed to fetch product ${detail}`);\r\n  const productData = (await res.json()).data;\r\n\r\n  // 2) Generate your JSON‑LD schema\r\n  const productSchema = generateProductSchema({\r\n    name:         productData.name,\r\n    description:  productData.description,\r\n    image:        productData.image,\r\n    brand:        productData.brand,\r\n    price:        productData.price,\r\n    currency:     productData.currency,\r\n    availability: productData.availability,\r\n    url:          `https://www.tradereply.com/marketplace/${detail}`,\r\n    seller:       productData.seller,\r\n    aggregateRating: productData.aggregateRating,\r\n    reviews:      productData.allReviews,\r\n    productData, // pass full data for any fallback logic\r\n  });\r\n\r\n  // 3) Build your metaProps\r\n  const metaProps = {\r\n    title:          `${productData.name} | TradeReply Marketplace`,\r\n    description:    productData.description,\r\n    canonical_link: `https://www.tradereply.com/marketplace/${detail}`,\r\n    og_title:       `${productData.name} | TradeReply Marketplace`,\r\n    og_description: productData.description,\r\n    og_site_name:   \"TradeReply\",\r\n    twitter_title:  `${productData.name} | TradeReply Marketplace`,\r\n    twitter_description: productData.description,\r\n  };\r\n\r\n  // 4) Wrap in RootLayout (head + providers) and HomeLayout (header/footer)\r\n  return (\r\n    <RootLayout metaProps={metaProps} schemas={[productSchema]}>\r\n      <HomeLayout>\r\n        {children}\r\n      </HomeLayout>\r\n    </RootLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;;;AAChD;AACA;AACA;;;;;AAEO,MAAM,UAAU,iBAAiB,kCAAkC;AAE3D,eAAe,wBAAwB,EAAE,QAAQ,EAAE,MAAM,EAAE;IACxE,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,qDAAqD;IACrD,MAAM,MAAM,MAAM,MAChB,6DAAwC,oBAAoB,EAAE,QAAQ;IAExE,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,QAAQ;IAChE,MAAM,cAAc,CAAC,MAAM,IAAI,IAAI,EAAE,EAAE,IAAI;IAE3C,kCAAkC;IAClC,MAAM,gBAAgB,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD,EAAE;QAC1C,MAAc,YAAY,IAAI;QAC9B,aAAc,YAAY,WAAW;QACrC,OAAc,YAAY,KAAK;QAC/B,OAAc,YAAY,KAAK;QAC/B,OAAc,YAAY,KAAK;QAC/B,UAAc,YAAY,QAAQ;QAClC,cAAc,YAAY,YAAY;QACtC,KAAc,CAAC,uCAAuC,EAAE,QAAQ;QAChE,QAAc,YAAY,MAAM;QAChC,iBAAiB,YAAY,eAAe;QAC5C,SAAc,YAAY,UAAU;QACpC;IACF;IAEA,0BAA0B;IAC1B,MAAM,YAAY;QAChB,OAAgB,GAAG,YAAY,IAAI,CAAC,yBAAyB,CAAC;QAC9D,aAAgB,YAAY,WAAW;QACvC,gBAAgB,CAAC,uCAAuC,EAAE,QAAQ;QAClE,UAAgB,GAAG,YAAY,IAAI,CAAC,yBAAyB,CAAC;QAC9D,gBAAgB,YAAY,WAAW;QACvC,cAAgB;QAChB,eAAgB,GAAG,YAAY,IAAI,CAAC,yBAAyB,CAAC;QAC9D,qBAAqB,YAAY,WAAW;IAC9C;IAEA,0EAA0E;IAC1E,qBACE,8OAAC,oHAAA,CAAA,UAAU;QAAC,WAAW;QAAW,SAAS;YAAC;SAAc;kBACxD,cAAA,8OAAC,4HAAA,CAAA,UAAU;sBACR;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}