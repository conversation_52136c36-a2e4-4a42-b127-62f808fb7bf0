/* [project]/src/css/common/CommonSearch.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.commonSearch {
  display: flex;
  position: relative;
  align-items: center;
}

.commonSearch.searchBtn .form-control {
  padding: .5rem 1rem;
  background-color: #fff;
  color: #000;
  border: 0;
  border-top-left-radius: 10rem;
  border-bottom-left-radius: 10rem;
  min-height: 50px;
  width: calc(100% - 54px);
  min-width: auto;
  font-weight: 600;
}

.commonSearch.searchBtn .form-control::placeholder {
  color: #c5c5d5;
  opacity: 1;
}

.commonSearch.searchBtn .form-control:focus {
  box-shadow: none;
  outline: 0;
  background-color: #fff;
  color: #000;
}

.commonSearch .form-control {
  padding: .5rem 1rem;
  padding-left: 50px;
  background-color: #ffffff4d;
  color: #fff;
  border: 0;
  border-radius: 15px;
  min-height: 70px;
  width: auto;
  width: 400px;
  font-size: 1.25rem;
  appearance: none;
  -webkit-appearance: none;
}

@media (width <= 991px) {
  .commonSearch .form-control {
    font-size: 16px;
    min-height: 56px;
    padding-left: 40px;
  }
}

.commonSearch .form-control:hover {
  appearance: none;
}

.commonSearch .form-control::placeholder {
  color: #fffc;
  opacity: 1;
}

.commonSearch .form-control:disabled {
  background-color: #0000;
}

.commonSearch .form-control:focus {
  box-shadow: none;
  outline: 0;
  background-color: #ffffff4d;
  color: #fff;
  border: 0;
}

.commonSearch .onlyIcon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}

@media (width <= 991px) {
  .commonSearch .onlyIcon {
    left: 15px;
  }
}

.commonSearch .btnIcon {
  cursor: pointer;
  width: 54px;
  min-height: 50px;
  background-color: #00adef;
  border-top-right-radius: 10rem;
  border-bottom-right-radius: 10rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all .3s ease-in-out;
}

.commonSearch .btnIcon:hover {
  background-color: #fea500;
}


/* [project]/src/css/common/Select.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.common_select {
  margin-bottom: 1.25rem;
}

.common_select .select__control {
  font-size: 1.25rem;
  min-height: 56px;
  transition: none;
  box-shadow: none;
  border: none;
  background-color: #0000;
  padding: .625rem 1rem;
  border-radius: 1rem;
  border: 1px solid #666;
}

@media (width <= 1599px) {
  .common_select .select__control {
    min-height: 52px;
    font-size: 1rem;
    padding: .625rem 1rem;
  }
}

.common_select .select__control:hover, .common_select .select__control:focus {
  border-color: #666;
}

.common_select .select__control .select__input-container {
  color: #fff;
}

.common_select .select__control .select__input {
  opacity: 0 !important;
}

.common_select .select__control .select__placeholder {
  color: #fff;
}

.common_select .select__control .select__value-container {
  padding-inline: 0;
}

.common_select .select__control .select__value-container .select__multi-value {
  background-color: #00adef;
  color: #fff;
}

.common_select .select__control .select__value-container .select__multi-value .select__multi-value__label {
  color: #fff;
}

.common_select .select__control .select__value-container--is-multi {
  flex-wrap: unset;
  overflow-x: auto;
  overflow-y: hidden;
}

.common_select .select__control .select__value-container--is-multi .select__multi-value {
  min-width: max-content;
}

.common_select .select__control .select__single-value {
  color: #fff;
  display: flex;
  align-items: center;
  gap: 10px;
}

.common_select .select__control .select__indicator-separator {
  display: none;
}

.common_select .select__control .select__indicator {
  cursor: pointer;
  padding: 0;
}

.common_select .select__control .select__indicator svg {
  fill: #fff;
  width: 24px;
  height: 24px;
  transition: all .3s ease-in-out;
}

.common_select .select__menu {
  width: 100%;
  right: 0;
  left: unset;
  background-color: #000;
  margin-bottom: 0;
  margin-top: 0;
}

.common_select .select__menu .select__menu-notice {
  font-size: 14px;
}

.common_select .select__menu .select__menu-list .select__option {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  color: #fff;
}

.common_select .select__menu .select__menu-list .select__option:hover {
  background-color: #0000;
  color: #00adef !important;
}

.common_select .select__menu .select__menu-list .select__option.select__option--is-focused {
  background-color: #0000;
  color: #fff;
}

.common_select .select__menu .select__menu-list .select__option.select__option--is-selected {
  background-color: #0000;
  color: #00adef !important;
}

.common_select .select__control--menu-is-open .select__indicator svg {
  transform: rotate(-180deg);
}


/* [project]/src/css/common/CustomBreadcrumb.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.custom_breadcrumb .secondary_link {
  cursor: pointer;
}

.custom_breadcrumb .breadcrumb li a {
  color: #c5c5d5;
}

.custom_breadcrumb .breadcrumb li a:hover {
  color: #00adef;
}

.custom_breadcrumb .breadcrumb li.active {
  color: #fff;
}

.custom_breadcrumb .breadcrumb li:before {
  color: #fff;
}


/* [project]/src/css/Home/Marketplace.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.marketplace {
  padding: 5rem 0;
}

.marketplace_inner_heading {
  border-bottom: 3px solid #00adef4d;
  margin-bottom: 30px;
  padding-bottom: 1.25rem;
}

.marketplace_inner_heading h4 {
  margin-bottom: 1.25rem;
  font-size: 1.65rem;
  line-height: 35px;
  font-weight: 600;
}

.marketplace_inner_heading .breadcrumb {
  margin: 0;
}

.marketplace_heading h1 {
  font-size: 3rem;
  font-weight: 800;
}

@media (width <= 1199px) {
  .marketplace_heading h1 {
    font-size: 2.5rem;
  }
}

@media (width <= 767px) {
  .marketplace_heading h1 {
    font-size: 1.5rem;
  }
}

@media (width <= 390px) {
  .marketplace_heading h1 {
    font-size: 1.3rem;
  }
}

.marketplace_shopcart {
  margin: 30px 0;
}

@media screen and (width <= 767px) {
  .marketplace_shopcart {
    flex-wrap: wrap;
  }
}

.marketplace_shopcart .education_search {
  margin: 0 50px;
  width: 450px;
}

@media screen and (width <= 991px) {
  .marketplace_shopcart .education_search {
    width: 350px;
    margin: 0 30px;
  }
}

@media screen and (width <= 767px) {
  .marketplace_shopcart .education_search {
    width: 100%;
    margin: 0;
    padding-top: 1.25rem;
  }
}

.marketplace_shopcart .education_search .commonSearch {
  max-width: 100%;
  width: 100%;
}

.marketplace_shopcart_btn button {
  background-color: #0000;
  border: 0;
  font-weight: 600;
  font-size: 1.25rem;
  color: #fff;
  transition: all .3s ease-in-out;
}

.marketplace_shopcart_btn button svg {
  margin-right: .625rem;
}

.marketplace_shopcart_btn button:hover {
  background-color: #0000;
  color: #00adef;
}

.marketplace .common_select {
  margin-bottom: 0;
}

.marketplace .common_select .select__control {
  padding: 0;
  border: 0;
  min-width: 80px;
  min-height: auto;
}

.marketplace_products_sellerInfo {
  margin: 20px 0;
  border-top: 3px solid #00adef4d;
}

@media (width <= 767px) {
  .marketplace_products_sellerInfo {
    border-top: none;
  }
}

.marketplace_products_sellerInfo h4 {
  margin: 20px 0;
}

.marketplace_products_sellerInfo .sellerProfile {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.marketplace_products_sellerInfo .sellerProfile img {
  min-height: 50px;
  max-height: 50px;
  min-width: 50px;
  max-width: 50px;
  border-radius: 50px;
}

.marketplace_products_sellerInfo .sellerProfile p {
  font-size: 22px;
  font-weight: 600;
}

.marketplace_products_sellerInfo .sellerRating {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 20px;
}

.marketplace_products_sellerInfo .sellerRating img {
  height: 25px;
}

.marketplace_products_sellerInfo .sellerRating span {
  font-size: 24px;
  font-weight: 600;
}

.marketplace_products_sellerInfo .sellerBtn button {
  width: 100%;
  min-height: 50px;
}

.marketplace_products_filter .accordion {
  border-radius: 0;
}

.marketplace_products_filter .accordion-item {
  background-color: #0000;
  border: 0;
  border-bottom: 3px solid #00adef33;
  border-radius: 0;
}

.marketplace_products_filter .accordion-button {
  background-color: #0000;
  border: 0;
  color: #fff;
  font-weight: 600;
  font-size: 1.25rem;
  padding: 1.5rem 0;
  text-transform: capitalize;
  border-radius: 0;
}

@media screen and (width <= 991px) {
  .marketplace_products_filter .accordion-button {
    font-size: 1rem;
    padding: 1rem 0;
  }
}

.marketplace_products_filter .accordion-button:focus {
  box-shadow: none;
}

.marketplace_products_filter .accordion-button:not(.collapsed) {
  box-shadow: none;
  background-color: #0000;
  color: #fff;
}

.marketplace_products_filter .accordion-button:not(.collapsed):after {
  background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-minus.svg");
  transform: none;
}

.marketplace_products_filter .accordion-button:after {
  background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg");
  transform: none;
  background-position: center;
}

.marketplace_products_filter .accordion-body {
  padding: 1rem 0 1.25rem;
}

.marketplace_products_filter .accordion-body button {
  background-color: #0000;
  border: 0;
  display: block;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
  padding: 6px 0;
  width: 100%;
  transition: all .3s ease-in-out;
  text-align: left;
}

.marketplace_products_filter .accordion-body button:hover {
  color: #00adef;
}

@media screen and (width <= 991px) {
  .marketplace_products_sort {
    justify-content: flex-end;
  }
}

.marketplace_products_sort h5 {
  font-weight: 500;
  margin-right: 5px;
}

.marketplace_products_sort .common_select .select__control {
  min-width: 150px;
}

.marketplace_products_card {
  margin-top: 30px;
}

.marketplace_products_card_rating {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 20px;
}

.marketplace_products_card_rating img {
  height: 25px;
}

.marketplace_products_card_rating span {
  font-size: 24px;
  font-weight: 600;
  color: #fff;
}

@media screen and (width <= 991px) {
  .marketplace_products_card_content h4 {
    font-size: 20px;
    line-height: 30px;
  }
}

@media screen and (width <= 767px) {
  .marketplace_products_card_content h4 {
    font-size: 16px;
    line-height: 24px;
  }
}

.marketplace_products_card_img {
  position: relative;
}

.marketplace_products_card_img:after {
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding-top: 90%;
  display: block;
}

.marketplace_products_card_img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  border-radius: 20px;
}

.marketplace .custom_breadcrumb .home-item {
  display: none;
}

.marketplace .custom_breadcrumb .secondary_link {
  padding: 0;
}

.marketplace .custom_breadcrumb .secondary_link:before {
  display: none;
}

.marketplace .marketplace_products_card_rating img {
  height: 25px;
  width: 25px;
}

@media screen and (width <= 576px) {
  .marketplace .marketplace_products_card_rating img {
    height: 18px;
    width: 18px;
  }
}

.marketplace .marketplace_products_card_rating span {
  font-size: 16px;
}

@media screen and (width <= 576px) {
  .marketplace .marketplace_products_card_rating span {
    font-size: 14px;
  }
}

.marketplace .megaMenu {
  position: relative;
}

.marketplace .megaMenu__toggle {
  padding: .5rem 1rem;
  color: #fff;
  font-family: Gilroy-SemiBold;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: .5rem;
  background: none;
  border: none;
}

.marketplace .megaMenu__dropdown {
  position: absolute;
  display: flex;
  top: 60px;
  left: -50px;
  width: 300px;
  max-width: 1059px;
  background: #fff;
  border: 1px solid #ffffff4d;
  box-shadow: 0 4px 10px #ffffff26;
  border-radius: 15px;
  margin-top: .5rem;
  overflow: hidden;
  transition: width .3s;
  z-index: 9999;
}

.marketplace .megaMenu__dropdown.expanded {
  width: 1059px;
}

.marketplace .megaMenu__categories {
  width: 300px;
  padding: 0;
  border-right: 1px solid #d9d9d9;
}

.marketplace .megaMenu__category {
  margin: 0;
  padding: 0;
  width: 300px;
  cursor: pointer;
}

.marketplace .megaMenu__category.active, .marketplace .megaMenu__category:hover {
  background-color: #e6e6e6 !important;
}

.marketplace .megaMenu__category-link {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: .75rem 1rem;
  text-decoration: none;
  color: #000;
  font-weight: 600;
  font-size: 20px;
  font-family: Gilroy;
  width: 100%;
  height: 100%;
}

.marketplace .megaMenu__icon {
  width: 1rem;
  height: 1rem;
}

.marketplace .megaMenu__subcategories {
  padding: 1.5rem;
  width: 100%;
  opacity: 0;
  transition: opacity .2s;
  pointer-events: none;
}

.marketplace .megaMenu__subcategories.visible {
  opacity: 1;
  pointer-events: auto;
}

.marketplace .megaMenu__columns {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.marketplace .megaMenu__column {
  min-width: 220px;
}

.marketplace .megaMenu__subtitle {
  color: #000;
  font-family: Gilroy;
  font-weight: 700;
  font-size: 16px;
  margin-bottom: .5rem;
}

.marketplace .megaMenu__items {
  list-style: none;
  padding: 0;
  margin: 0;
}

.marketplace .megaMenu__link {
  display: block;
  padding: .3rem 0;
  font-size: 16px;
  color: #000c;
  font-weight: 500;
  white-space: normal;
  overflow-wrap: break-word;
}

.marketplace .megaMenu__link:hover {
  text-decoration: underline;
}

@media (width <= 1050px) and (width >= 728px) {
  .marketplace .megaMenu__dropdown {
    position: absolute;
    top: 60px;
    left: -30px;
    width: 247px;
    max-width: 750px;
    height: auto;
    overflow: hidden;
    flex-direction: row !important;
    border-radius: 15px !important;
  }

  .marketplace .megaMenu__categories {
    border-right: 1px solid #d9d9d9;
    width: 100% !important;
  }

  .marketplace .megaMenu__category {
    width: 247px !important;
  }

  .marketplace .megaMenu__subcategories {
    width: 100% !important;
    padding: 1rem !important;
    opacity: 1 !important;
    pointer-events: auto !important;
  }

  .marketplace .megaMenu__columns {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

@media (width <= 727px) {
  .marketplace .megaMenu__dropdown {
    flex-direction: column;
    position: fixed;
    top: 8%;
    left: 0;
    right: 0;
    height: 92vh;
    width: 100%;
    background: #ffffffe5;
    border-radius: 0;
    backdrop-filter: blur(10px);
    overflow-y: auto;
    max-height: 100vh;
    -webkit-overflow-scrolling: touch;
    z-index: 9999;
  }

  .marketplace .megaMenu__category {
    width: 100%;
  }

  .marketplace .megaMenu__mobile-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #ccc;
    color: #000;
    font-weight: 700;
    font-size: 20px;
    font-family: Gilroy;
  }

  .marketplace .megaMenu__back, .marketplace .megaMenu__close {
    background: none;
    border: none;
    font-size: 22px;
    font-weight: bold;
    cursor: pointer;
    margin-right: 8px;
  }

  .marketplace .megaMenu__logo {
    color: #000;
    font-weight: 700;
    font-size: 20px;
    font-family: Gilroy;
    margin-left: 8px;
  }

  .marketplace .megaMenu__categories, .marketplace .megaMenu__subcategories {
    width: 100%;
    padding: 1rem;
  }

  .marketplace .megaMenu .megaMenu__link {
    font-size: 15px;
    line-height: 1.4;
    padding-top: 8px !important;
    padding-bottom: 8px !important;
    white-space: normal !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    display: block !important;
    width: 100% !important;
  }

  .marketplace .megaMenu__columns {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (width <= 388px) {
  .marketplace .megaMenu__dropdown {
    flex-direction: column;
    position: fixed;
    top: 11%;
    left: 0;
    right: 0;
    height: 89vh;
    width: 100%;
    background: #ffffffe5;
    border-radius: 0;
    backdrop-filter: blur(10px);
    overflow-y: auto;
    max-height: 100vh;
    -webkit-overflow-scrolling: touch;
    z-index: 9999;
  }

  .marketplace .megaMenu .megaMenu__column {
    max-width: 310px !important;
  }

  .marketplace .megaMenu .megaMenu__link {
    max-width: 100% !important;
  }
}


/*# sourceMappingURL=src_css_37aab737._.css.map*/
