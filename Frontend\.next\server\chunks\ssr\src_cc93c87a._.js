module.exports = {

"[project]/src/assets/svgIcons/SvgIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AccessBaseBlue": (()=>AccessBaseBlue),
    "AccessDarkBlue": (()=>AccessDarkBlue),
    "AccessGray": (()=>AccessGray),
    "AddBlueIcon": (()=>AddBlueIcon),
    "AddPlusIcon": (()=>AddPlusIcon),
    "ArtArrowIcon": (()=>ArtArrowIcon),
    "BaseEyeIcon": (()=>BaseEyeIcon),
    "BlackCross": (()=>BlackCross),
    "BlackDownArrow": (()=>BlackDownArrow),
    "BlackDownIcon": (()=>BlackDownIcon),
    "BlackDropDownArrowIcon": (()=>BlackDropDownArrowIcon),
    "BlackErrorCircle": (()=>BlackErrorCircle),
    "BlackShareIcon": (()=>BlackShareIcon),
    "BlueLocationIcon": (()=>BlueLocationIcon),
    "BrushIcon": (()=>BrushIcon),
    "BulletPointIcon": (()=>BulletPointIcon),
    "CalculatorIcon": (()=>CalculatorIcon),
    "CartIcon": (()=>CartIcon),
    "CartSideIcon": (()=>CartSideIcon),
    "ChartIcon": (()=>ChartIcon),
    "CheckGradientIcon": (()=>CheckGradientIcon),
    "CheckIcon": (()=>CheckIcon),
    "CheckoutCardBaseBlue": (()=>CheckoutCardBaseBlue),
    "CheckoutCardDarkBlue": (()=>CheckoutCardDarkBlue),
    "CheckoutCardGray": (()=>CheckoutCardGray),
    "ClockWhiteIcon": (()=>ClockWhiteIcon),
    "CloseEye": (()=>CloseEye),
    "CoinWhiteIcon": (()=>CoinWhiteIcon),
    "ContactCustomerSupport": (()=>ContactCustomerSupport),
    "CrossIcon": (()=>CrossIcon),
    "DashboardIcon": (()=>DashboardIcon),
    "DeleteDarkIcon": (()=>DeleteDarkIcon),
    "DeleteIcon": (()=>DeleteIcon),
    "DeviceMobileSpeaker": (()=>DeviceMobileSpeaker),
    "DigitaLAssetIcon": (()=>DigitaLAssetIcon),
    "DollerIcon": (()=>DollerIcon),
    "DragDropIcon": (()=>DragDropIcon),
    "DropArrowIcon": (()=>DropArrowIcon),
    "DropArrowUpIcon": (()=>DropArrowUpIcon),
    "DropDownArrowIcon": (()=>DropDownArrowIcon),
    "DynamicIcon": (()=>DynamicIcon),
    "EditIcon": (()=>EditIcon),
    "EditIconSvg": (()=>EditIconSvg),
    "EyeDarkIcon": (()=>EyeDarkIcon),
    "EyeDarkInsightIcon": (()=>EyeDarkInsightIcon),
    "EyeIcon": (()=>EyeIcon),
    "FilterIcon": (()=>FilterIcon),
    "FlatBlueBook": (()=>FlatBlueBook),
    "FollowersIcon": (()=>FollowersIcon),
    "GlobalBlueIcons": (()=>GlobalBlueIcons),
    "GlobalIcons": (()=>GlobalIcons),
    "GraphsIcon": (()=>GraphsIcon),
    "GreenCheckStarIcon": (()=>GreenCheckStarIcon),
    "GreyCheckIcon": (()=>GreyCheckIcon),
    "GreyCrossIcon": (()=>GreyCrossIcon),
    "GuageWhiteIcon": (()=>GuageWhiteIcon),
    "HelpIcon": (()=>HelpIcon),
    "KpiIcon": (()=>KpiIcon),
    "LearningIcon": (()=>LearningIcon),
    "LicenseIcon": (()=>LicenseIcon),
    "LightEyeIcon": (()=>LightEyeIcon),
    "LinkIcon": (()=>LinkIcon),
    "LocationIconSvg": (()=>LocationIconSvg),
    "LockIcon": (()=>LockIcon),
    "LogoutIcon": (()=>LogoutIcon),
    "MarketplaceDisputeIcon": (()=>MarketplaceDisputeIcon),
    "MarketplaceListIcon": (()=>MarketplaceListIcon),
    "MinusIcon": (()=>MinusIcon),
    "MoneyWithWings": (()=>MoneyWithWings),
    "NextArrowIcon": (()=>NextArrowIcon),
    "NoteSettingBlueIcon": (()=>NoteSettingBlueIcon),
    "OpenNewtabIcon": (()=>OpenNewtabIcon),
    "PartnershipIcon": (()=>PartnershipIcon),
    "PaymentIcon": (()=>PaymentIcon),
    "PaymentIconSvg": (()=>PaymentIconSvg),
    "PinIcon": (()=>PinIcon),
    "PlusIcon": (()=>PlusIcon),
    "PlusIconSvg": (()=>PlusIconSvg),
    "PrevIcon": (()=>PrevIcon),
    "ProductFormatWhiteIcon": (()=>ProductFormatWhiteIcon),
    "ProfileUserDarkIcon": (()=>ProfileUserDarkIcon),
    "PublicProfileIcon": (()=>PublicProfileIcon),
    "PurchasedProductIcon": (()=>PurchasedProductIcon),
    "QuoteIcon": (()=>QuoteIcon),
    "RatingStarIcon": (()=>RatingStarIcon),
    "RealTimeIcon": (()=>RealTimeIcon),
    "RedCircleCrossIcon": (()=>RedCircleCrossIcon),
    "RedCrossIcon": (()=>RedCrossIcon),
    "RedErrorCircle": (()=>RedErrorCircle),
    "RedInfoIcon": (()=>RedInfoIcon),
    "RedInfoStarIcon": (()=>RedInfoStarIcon),
    "ReferIcon": (()=>ReferIcon),
    "RelistIcon": (()=>RelistIcon),
    "RemoveIconSvg": (()=>RemoveIconSvg),
    "RenameIcon": (()=>RenameIcon),
    "ResendCodeIcon": (()=>ResendCodeIcon),
    "RightArrowIcon": (()=>RightArrowIcon),
    "RightArrowIconSvg": (()=>RightArrowIconSvg),
    "RightSolidArrowIcon": (()=>RightSolidArrowIcon),
    "RightSolidArrowIconSvg": (()=>RightSolidArrowIconSvg),
    "RoketIcon": (()=>RoketIcon),
    "SearchIcons": (()=>SearchIcons),
    "SecurityIcon": (()=>SecurityIcon),
    "SellerDashboardIcon": (()=>SellerDashboardIcon),
    "SettingIcon": (()=>SettingIcon),
    "ShareLightStrIcon": (()=>ShareLightStrIcon),
    "ShuffleWhiteIcon": (()=>ShuffleWhiteIcon),
    "SignoutIcon": (()=>SignoutIcon),
    "SoldProductIcon": (()=>SoldProductIcon),
    "SolidIncon": (()=>SolidIncon),
    "SolidInfoIcon": (()=>SolidInfoIcon),
    "SolidRedArrowIcon": (()=>SolidRedArrowIcon),
    "SolidSettingIcon": (()=>SolidSettingIcon),
    "StaticListingImg": (()=>StaticListingImg),
    "ThemeIcon": (()=>ThemeIcon),
    "ThumbDownIcon": (()=>ThumbDownIcon),
    "ThumbUpIcon": (()=>ThumbUpIcon),
    "TopRightArrowIcon": (()=>TopRightArrowIcon),
    "TradeIcon": (()=>TradeIcon),
    "TradingPlatformWhiteIcon": (()=>TradingPlatformWhiteIcon),
    "TrendIcon": (()=>TrendIcon),
    "TripleDotsMenu": (()=>TripleDotsMenu),
    "UserBlackIcon": (()=>UserBlackIcon),
    "UserBlueIcon": (()=>UserBlueIcon),
    "UserBluekIcon": (()=>UserBluekIcon),
    "UserSolidBlueIcon": (()=>UserSolidBlueIcon),
    "ViewCartBaseBlue": (()=>ViewCartBaseBlue),
    "ViewCartDarkBlue": (()=>ViewCartDarkBlue),
    "ViewCartGray": (()=>ViewCartGray),
    "WhiteCrossCircle": (()=>WhiteCrossCircle),
    "WhiteCrossIcon": (()=>WhiteCrossIcon),
    "WhiteDoubleStack": (()=>WhiteDoubleStack),
    "WhiteDownArrow": (()=>WhiteDownArrow),
    "WhiteDropDownArrow": (()=>WhiteDropDownArrow),
    "WhiteInfoIcon": (()=>WhiteInfoIcon),
    "WhiteSingleStack": (()=>WhiteSingleStack),
    "WhiteTripleStack": (()=>WhiteTripleStack),
    "YellowInfoHexa": (()=>YellowInfoHexa)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const EyeIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "18",
        height: "12",
        viewBox: "0 0 18 12",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M9 9.70508C9.9375 9.70508 10.7345 9.37708 11.391 8.72108C12.047 8.06458 12.375 7.26758 12.375 6.33008C12.375 5.39258 12.047 4.59558 11.391 3.93908C10.7345 3.28308 9.9375 2.95508 9 2.95508C8.0625 2.95508 7.2655 3.28308 6.609 3.93908C5.953 4.59558 5.625 5.39258 5.625 6.33008C5.625 7.26758 5.953 8.06458 6.609 8.72108C7.2655 9.37708 8.0625 9.70508 9 9.70508ZM9 8.35508C8.4375 8.35508 7.9595 8.15808 7.566 7.76408C7.172 7.37058 6.975 6.89258 6.975 6.33008C6.975 5.76758 7.172 5.28933 7.566 4.89533C7.9595 4.50183 8.4375 4.30508 9 4.30508C9.5625 4.30508 10.0408 4.50183 10.4347 4.89533C10.8282 5.28933 11.025 5.76758 11.025 6.33008C11.025 6.89258 10.8282 7.37058 10.4347 7.76408C10.0408 8.15808 9.5625 8.35508 9 8.35508ZM9 11.9551C7.175 11.9551 5.5125 11.4456 4.0125 10.4266C2.5125 9.40808 1.425 8.04258 0.75 6.33008C1.425 4.61758 2.5125 3.25183 4.0125 2.23283C5.5125 1.21433 7.175 0.705078 9 0.705078C10.825 0.705078 12.4875 1.21433 13.9875 2.23283C15.4875 3.25183 16.575 4.61758 17.25 6.33008C16.575 8.04258 15.4875 9.40808 13.9875 10.4266C12.4875 11.4456 10.825 11.9551 9 11.9551ZM9 10.4551C10.4125 10.4551 11.7095 10.0831 12.891 9.33908C14.072 8.59558 14.975 7.59258 15.6 6.33008C14.975 5.06758 14.072 4.06433 12.891 3.32033C11.7095 2.57683 10.4125 2.20508 9 2.20508C7.5875 2.20508 6.2905 2.57683 5.109 3.32033C3.928 4.06433 3.025 5.06758 2.4 6.33008C3.025 7.59258 3.928 8.59558 5.109 9.33908C6.2905 10.0831 7.5875 10.4551 9 10.4551Z",
            fill: "#101014"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 10,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 3,
        columnNumber: 5
    }, this);
};
const CloseEye = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "22.016",
        height: "17.613",
        viewBox: "0 0 22.016 17.613",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            id: "Icon_awesome-eye-slash",
            "data-name": "Icon awesome-eye-slash",
            d: "M11.008,13.76A4.935,4.935,0,0,1,6.092,9.181L2.484,6.392A11.465,11.465,0,0,0,1.221,8.3a1.113,1.113,0,0,0,0,1,11.033,11.033,0,0,0,9.787,6.1,10.685,10.685,0,0,0,2.679-.36L11.9,13.67a4.958,4.958,0,0,1-.894.09Zm10.8,2L18,12.819A11.4,11.4,0,0,0,20.8,9.308a1.113,1.113,0,0,0,0-1,11.033,11.033,0,0,0-9.787-6.1A10.6,10.6,0,0,0,5.94,3.5L1.564.116a.55.55,0,0,0-.773.1l-.675.869a.55.55,0,0,0,.1.772L20.452,17.5a.55.55,0,0,0,.773-.1l.676-.869a.55.55,0,0,0-.1-.772Zm-6.32-4.885L14.131,9.829a3.26,3.26,0,0,0-3.994-4.194,1.639,1.639,0,0,1,.32.97,1.6,1.6,0,0,1-.053.344L7.872,4.992a4.9,4.9,0,0,1,3.136-1.139,4.951,4.951,0,0,1,4.954,4.954,4.836,4.836,0,0,1-.478,2.068Z",
            transform: "translate(0 0)",
            fill: "#fff"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 24,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 18,
        columnNumber: 3
    }, this);
const ThemeIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "32",
        height: "32",
        viewBox: "0 0 32 32",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
            id: "Group_175598",
            "data-name": "Group 175598",
            transform: "translate(-1847 -26)",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                id: "Group_175597",
                "data-name": "Group 175597",
                transform: "translate(1842.007 28.533)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                        id: "Rectangle_13566",
                        "data-name": "Rectangle 13566",
                        width: "32",
                        height: "32",
                        rx: "16",
                        transform: "translate(4.993 -2.533)",
                        fill: "#f45126"
                    }, void 0, false, {
                        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                        lineNumber: 51,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        id: "Group_175601",
                        "data-name": "Group 175601",
                        transform: "translate(6.923 -0.604)",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                id: "Path_113806",
                                "data-name": "Path 113806",
                                d: "M41.464,28.649a4.427,4.427,0,0,1-.409.578.185.185,0,1,0,.283.237,4.839,4.839,0,0,0,.444-.625.185.185,0,0,0-.317-.189Zm.521-1.312a4.5,4.5,0,0,1-.208.677.185.185,0,0,0,.343.136,4.837,4.837,0,0,0,.225-.733.184.184,0,1,0-.36-.08Zm.086-1.409a4.537,4.537,0,0,1,.012.708.184.184,0,1,0,.368.023,4.831,4.831,0,0,0-.013-.766.185.185,0,1,0-.367.035Zm-.355-1.366a4.469,4.469,0,0,1,.231.669.185.185,0,0,0,.357-.093,4.833,4.833,0,0,0-.251-.724.184.184,0,1,0-.338.148Zm-.764-1.187a4.508,4.508,0,0,1,.43.563.185.185,0,1,0,.31-.2,4.786,4.786,0,0,0-.465-.609.185.185,0,1,0-.275.246Z",
                                transform: "translate(-23.867 -12.612)",
                                fill: "#fff",
                                fillRule: "evenodd"
                            }, void 0, false, {
                                fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                                lineNumber: 65,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                id: "Path_113807",
                                "data-name": "Path 113807",
                                d: "M13.748,7.475a6.273,6.273,0,1,0,6.273,6.273A6.276,6.276,0,0,0,13.748,7.475Zm.369.75a5.535,5.535,0,0,1,0,11.046ZM12.7,4.095v1.7a1.048,1.048,0,0,0,2.1,0v-1.7a1.048,1.048,0,0,0-2.1,0Zm7.133,2.087-1.2,1.2a1.048,1.048,0,0,0,1.482,1.482l1.2-1.2a1.048,1.048,0,1,0-1.482-1.482ZM23.4,12.7h-1.7a1.048,1.048,0,0,0,0,2.1h1.7a1.048,1.048,0,0,0,0-2.1Zm-2.087,7.133-1.2-1.2a1.048,1.048,0,0,0-1.482,1.482l1.2,1.2a1.048,1.048,0,1,0,1.482-1.482ZM14.8,23.4v-1.7a1.048,1.048,0,0,0-2.1,0v1.7a1.048,1.048,0,0,0,2.1,0ZM7.663,21.315l1.2-1.2a1.048,1.048,0,0,0-1.482-1.482l-1.2,1.2a1.048,1.048,0,1,0,1.482,1.482ZM4.095,14.8h1.7a1.048,1.048,0,0,0,0-2.1h-1.7a1.048,1.048,0,0,0,0,2.1ZM6.181,7.663l1.2,1.2A1.048,1.048,0,0,0,8.863,7.381l-1.2-1.2A1.048,1.048,0,0,0,6.181,7.663Z",
                                fill: "#fff",
                                fillRule: "evenodd"
                            }, void 0, false, {
                                fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                                lineNumber: 73,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                        lineNumber: 60,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                lineNumber: 46,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 41,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 35,
        columnNumber: 5
    }, this);
};
const GlobalIcons = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-black-global.svg",
        alt: "Global Icons"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 88,
        columnNumber: 5
    }, this);
};
const GlobalBlueIcons = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-blue-global.svg",
        alt: "Global Blue Icons"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 93,
        columnNumber: 5
    }, this);
};
const UserBlackIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-black.svg",
        alt: "User Black Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 98,
        columnNumber: 5
    }, this);
};
const UserBluekIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-brand-blue.svg",
        alt: "User Blue Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 103,
        columnNumber: 5
    }, this);
};
const UserSolidBlueIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-account.svg",
        alt: "User Solid Blue Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 108,
        columnNumber: 5
    }, this);
};
const SearchIcons = ({ width = 18, height = 18 })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-search.svg",
        width: width,
        height: height,
        alt: "Search Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 113,
        columnNumber: 5
    }, this);
};
const RoketIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-rocket.svg",
        alt: "Rocket Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 118,
        columnNumber: 5
    }, this);
};
const FilterIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-filter.svg",
        alt: "Filter Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 123,
        columnNumber: 5
    }, this);
};
const DashboardIcon = ({ color })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradreply-dashboard.svg",
        alt: "Dashboard Icon",
        className: color
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 128,
        columnNumber: 5
    }, this);
};
const DynamicIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-dynamic.svg",
        alt: "Dynamic Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 137,
        columnNumber: 5
    }, this);
};
const KpiIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-kpi.svg",
        alt: "KPI Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 142,
        columnNumber: 5
    }, this);
};
const GraphsIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradreply-graphs.svg",
        alt: "Graph Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 147,
        columnNumber: 5
    }, this);
};
const ChartIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-charts.svg",
        alt: "Chart Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 152,
        columnNumber: 5
    }, this);
};
const TrendIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trend.svg",
        alt: "Trend Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 157,
        columnNumber: 5
    }, this);
};
const RealTimeIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-real-time.svg",
        alt: "Real Time Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 162,
        columnNumber: 5
    }, this);
};
const BrushIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-customize.svg",
        alt: "Brush Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 167,
        columnNumber: 5
    }, this);
};
const LearningIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-learning.svg",
        alt: "Learning Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 172,
        columnNumber: 5
    }, this);
};
const NextArrowIcon = ()=>{
    return(// <svg
    //   width="26"
    //   height="22"
    //   viewBox="0 0 26 22"
    //   fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
    // >
    //   <path
    //     d="M13.9387 21.1081C13.7989 20.9687 13.6879 20.8031 13.6122 20.6208C13.5365 20.4385 13.4975 20.243 13.4975 20.0456C13.4975 19.8481 13.5365 19.6527 13.6122 19.4703C13.6879 19.288 13.7989 19.1224 13.9387 18.9831L20.3749 12.5468L1.99995 12.5468C1.60212 12.5468 1.2206 12.3888 0.93929 12.1075C0.657985 11.8262 0.49995 11.4446 0.49995 11.0468C0.49995 10.649 0.657985 10.2675 0.939291 9.98616C1.2206 9.70485 1.60212 9.54682 1.99995 9.54682L20.3749 9.54682L13.9387 3.10807C13.6569 2.82628 13.4986 2.44409 13.4986 2.04557C13.4986 1.64706 13.6569 1.26486 13.9387 0.98307C14.2205 0.701278 14.6027 0.542968 15.0012 0.542968C15.3997 0.542968 15.7819 0.701278 16.0637 0.98307L25.0637 9.98307C25.2035 10.1224 25.3145 10.288 25.3902 10.4703C25.4659 10.6527 25.5049 10.8481 25.5049 11.0456C25.5049 11.243 25.4659 11.4385 25.3902 11.6208C25.3145 11.8031 25.2035 11.9687 25.0637 12.1081L16.0637 21.1081C15.9243 21.2479 15.7588 21.3589 15.5764 21.4346C15.3941 21.5103 15.1986 21.5493 15.0012 21.5493C14.8038 21.5493 14.6083 21.5103 14.426 21.4346C14.2436 21.3589 14.0781 21.2479 13.9387 21.1081Z"
    //     fill="white"
    //   />
    // </svg>
    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-next-arrow.svg",
        alt: "Next Arrow Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 189,
        columnNumber: 5
    }, this));
};
const PrevIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-prev-arrow.svg",
        alt: "Prev Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 194,
        columnNumber: 5
    }, this);
};
const QuoteIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-quote.svg",
        alt: "Quote Icon",
        width: "31",
        height: "26"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 199,
        columnNumber: 5
    }, this);
};
const CheckIcon = ({ height = 28, width = 28 })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-success.svg",
        alt: "Check Icon",
        width: width,
        height: height
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 207,
        columnNumber: 5
    }, this);
};
const RedCrossIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-red-x.svg",
        alt: "Red Cross Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 215,
        columnNumber: 5
    }, this);
};
const PlusIcon = ({ color, height = 32, width = 33 })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg",
        alt: "Plus Icon",
        width: width,
        height: height,
        className: color
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 220,
        columnNumber: 5
    }, this);
};
const MinusIcon = ({ width = '29px', height = '4px' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-minus.svg",
        height: height,
        width: width,
        alt: "Minus Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 229,
        columnNumber: 5
    }, this);
};
const RedInfoStarIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-red-warning-star.svg",
        alt: "Red Info Star Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 234,
        columnNumber: 5
    }, this);
};
const GreenCheckStarIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-green-check-star.svg",
        alt: "Green Check Star",
        width: "42",
        height: "42"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 239,
        columnNumber: 5
    }, this);
};
const NoteSettingBlueIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-note-setting.svg",
        alt: "Note Setting Blue Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 247,
        columnNumber: 5
    }, this);
};
const SettingIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "38",
        height: "40",
        viewBox: "0 0 38 40",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M19.0264 0.5C20.4944 0.516 21.9564 0.686 23.3904 1.006C23.6955 1.0741 23.9716 1.23581 24.1803 1.46852C24.389 1.70124 24.5198 1.99334 24.5544 2.304L24.8944 5.358C24.9424 5.78857 25.0908 6.20186 25.3277 6.56461C25.5645 6.92737 25.8832 7.22946 26.2581 7.44658C26.633 7.6637 27.0537 7.78979 27.4862 7.8147C27.9187 7.83961 28.351 7.76264 28.7484 7.59L31.5484 6.36C31.8329 6.2347 32.1493 6.20088 32.4538 6.26323C32.7584 6.32557 33.036 6.48099 33.2484 6.708C35.2725 8.87031 36.7803 11.4633 37.6584 14.292C37.7501 14.5895 37.7471 14.9081 37.6496 15.2038C37.5521 15.4994 37.3651 15.7574 37.1144 15.942L34.6324 17.774C34.2827 18.0303 33.9983 18.3655 33.8023 18.7522C33.6063 19.1389 33.5041 19.5664 33.5041 20C33.5041 20.4336 33.6063 20.8611 33.8023 21.2478C33.9983 21.6345 34.2827 21.9697 34.6324 22.226L37.1184 24.056C37.3695 24.2407 37.5568 24.499 37.6543 24.7951C37.7518 25.0912 37.7546 25.4102 37.6624 25.708C36.7849 28.5366 35.2778 31.1295 33.2544 33.292C33.0424 33.5189 32.7652 33.6745 32.4611 33.7372C32.1569 33.7999 31.8408 33.7666 31.5564 33.642L28.7444 32.408C28.3476 32.234 27.9154 32.1559 27.4827 32.18C27.0501 32.204 26.6292 32.3296 26.2542 32.5466C25.8791 32.7635 25.5604 33.0657 25.3238 33.4287C25.0872 33.7917 24.9394 34.2053 24.8924 34.636L24.5524 37.688C24.5184 37.995 24.3905 38.2841 24.1861 38.5157C23.9817 38.7473 23.7108 38.9101 23.4104 38.982C20.5136 39.6726 17.4951 39.6726 14.5984 38.982C14.2976 38.9105 14.0262 38.7478 13.8214 38.5162C13.6167 38.2845 13.4885 37.9953 13.4544 37.688L13.1164 34.64C13.0673 34.2106 12.9182 33.7987 12.6811 33.4374C12.4439 33.0761 12.1254 32.7754 11.751 32.5595C11.3766 32.3437 10.9568 32.2186 10.5253 32.1943C10.0938 32.1701 9.6626 32.2474 9.26638 32.42L6.45438 33.652C6.1697 33.7771 5.85318 33.8106 5.54862 33.7479C5.24406 33.6852 4.96652 33.5293 4.75438 33.302C2.73064 31.137 1.22419 28.5412 0.348384 25.71C0.256177 25.4122 0.259017 25.0932 0.35651 24.7971C0.454002 24.501 0.641304 24.2427 0.892384 24.058L3.37838 22.226C3.72808 21.9697 4.01246 21.6345 4.20848 21.2478C4.40451 20.8611 4.50666 20.4336 4.50666 20C4.50666 19.5664 4.40451 19.1389 4.20848 18.7522C4.01246 18.3655 3.72808 18.0303 3.37838 17.774L0.892384 15.946C0.641304 15.7613 0.454002 15.503 0.35651 15.2069C0.259017 14.9108 0.256177 14.5918 0.348384 14.294C1.22647 11.4653 2.73423 8.87231 4.75838 6.71C4.97075 6.48299 5.24841 6.32757 5.55296 6.26523C5.8575 6.20288 6.17389 6.2367 6.45838 6.362L9.25838 7.592C9.65642 7.76454 10.0894 7.84132 10.5225 7.81617C10.9556 7.79102 11.3767 7.66465 11.7521 7.44719C12.1275 7.22974 12.4467 6.92727 12.684 6.56408C12.9212 6.2009 13.07 5.78712 13.1184 5.356L13.4584 2.304C13.4927 1.99271 13.6236 1.69997 13.8327 1.46683C14.0418 1.23369 14.3186 1.07185 14.6244 1.004C16.0564 0.686667 17.5237 0.518667 19.0264 0.5ZM19.0024 14C17.4111 14 15.885 14.6321 14.7597 15.7574C13.6345 16.8826 13.0024 18.4087 13.0024 20C13.0024 21.5913 13.6345 23.1174 14.7597 24.2426C15.885 25.3679 17.4111 26 19.0024 26C20.5937 26 22.1198 25.3679 23.245 24.2426C24.3702 23.1174 25.0024 21.5913 25.0024 20C25.0024 18.4087 24.3702 16.8826 23.245 15.7574C22.1198 14.6321 20.5937 14 19.0024 14Z",
            fill: "#FEA500"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 258,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 252,
        columnNumber: 5
    }, this);
};
const SolidSettingIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-yellow-wrench.svg",
        alt: "Solid Setting Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 267,
        columnNumber: 5
    }, this);
};
const RightSolidArrowIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-right-solid-arrow.svg",
        alt: ""
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 272,
        columnNumber: 5
    }, this);
};
const SolidInfoIcon = ({ width = '20px', height = '20px' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-marker.svg",
        height: height,
        width: width,
        alt: "Info Marker Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 277,
        columnNumber: 5
    }, this);
};
const WhiteInfoIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-white.svg",
        alt: "White Info Marker Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 282,
        columnNumber: 5
    }, this);
};
const RightArrowIcon = ({ color })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-right-arrow.svg",
        alt: "Right Arrow Icon",
        className: color
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 287,
        columnNumber: 5
    }, this);
};
const CartIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "25",
        height: "20",
        viewBox: "0 0 25 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M20.8333 16C19.3533 16 18.1667 16.89 18.1667 18C18.1667 18.5304 18.4476 19.0391 18.9477 19.4142C19.4478 19.7893 20.1261 20 20.8333 20C21.5406 20 22.2189 19.7893 22.719 19.4142C23.219 19.0391 23.5 18.5304 23.5 18C23.5 17.4696 23.219 16.9609 22.719 16.5858C22.2189 16.2107 21.5406 16 20.8333 16ZM-0.5 0V2H2.16667L6.96667 9.59L5.15333 12.04C4.95333 12.32 4.83333 12.65 4.83333 13C4.83333 13.5304 5.11428 14.0391 5.61438 14.4142C6.11448 14.7893 6.79276 15 7.5 15H23.5V13H8.06C7.97159 13 7.88681 12.9737 7.8243 12.9268C7.76178 12.8799 7.72667 12.8163 7.72667 12.75C7.72667 12.7 7.74 12.66 7.76667 12.63L8.96667 11H18.9C19.9 11 20.78 10.58 21.2333 9.97L26.0067 3.5C26.1 3.34 26.1667 3.17 26.1667 3C26.1667 2.73478 26.0262 2.48043 25.7761 2.29289C25.5261 2.10536 25.187 2 24.8333 2H5.11333L3.86 0M7.5 16C6.02 16 4.83333 16.89 4.83333 18C4.83333 18.5304 5.11428 19.0391 5.61438 19.4142C6.11448 19.7893 6.79276 20 7.5 20C8.20724 20 8.88552 19.7893 9.38562 19.4142C9.88571 19.0391 10.1667 18.5304 10.1667 18C10.1667 17.4696 9.88571 16.9609 9.38562 16.5858C8.88552 16.2107 8.20724 16 7.5 16Z",
            fill: "white"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 301,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 294,
        columnNumber: 5
    }, this);
};
const SignoutIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-signout.svg",
        alt: "Signout Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 310,
        columnNumber: 5
    }, this);
};
const HelpIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradreply-helpicon.svg",
        alt: "Help Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 315,
        columnNumber: 5
    }, this);
};
const BaseEyeIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye.svg",
        alt: "Base Eye Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 320,
        columnNumber: 5
    }, this);
};
const UserBlueIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-blue.svg",
        alt: "User Blue Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 325,
        columnNumber: 5
    }, this);
};
const DollerIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-renew-dollar.svg",
        alt: "Dollar Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 330,
        columnNumber: 5
    }, this);
};
const SecurityIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-security.svg",
        alt: "Security Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 335,
        columnNumber: 5
    }, this);
};
const LockIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-lock.svg",
        alt: "Lock Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 340,
        columnNumber: 5
    }, this);
};
const LinkIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-links.svg",
        alt: "Link Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 345,
        columnNumber: 5
    }, this);
};
const PaymentIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-payment.svg",
        alt: "Payment Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 350,
        columnNumber: 5
    }, this);
};
const PaymentIconSvg = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "20",
        height: "17",
        viewBox: "0 0 20 17",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M18 0.5H2C0.89 0.5 0.00999999 1.39 0.00999999 2.5L0 14.5C0 15.61 0.89 16.5 2 16.5H18C19.11 16.5 20 15.61 20 14.5V2.5C20 1.39 19.11 0.5 18 0.5ZM18 14.5H2V8.5H18V14.5ZM18 4.5H2V2.5H18V4.5Z",
            fill: "#00ADEF"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 362,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 355,
        columnNumber: 5
    }, this);
};
const CartSideIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-carticon.svg",
        alt: "Cart Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 371,
        columnNumber: 5
    }, this);
};
const EditIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-editcon.svg",
        alt: "Edit Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 376,
        columnNumber: 5
    }, this);
};
const CrossIcon = ({ color })=>{
    return(// <svg
    //   width="18"
    //   height="19"
    //   viewBox="0 0 18 19"
    //   fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
    // >
    //   <path
    //     d="M9 0.75C4.125 0.75 0.25 4.625 0.25 9.5C0.25 14.375 4.125 18.25 9 18.25C13.875 18.25 17.75 14.375 17.75 9.5C17.75 4.625 13.875 0.75 9 0.75ZM12.375 13.875L9 10.5L5.625 13.875L4.625 12.875L8 9.5L4.625 6.125L5.625 5.125L9 8.5L12.375 5.125L13.375 6.125L10 9.5L13.375 12.875L12.375 13.875Z"
    //     fill="white"
    //   />
    // </svg>
    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-white-x.svg",
        alt: "White Cirle Cross Icon",
        width: "18",
        height: "19",
        className: color
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 394,
        columnNumber: 5
    }, this));
};
const PublicProfileIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-profile-user-light.svg",
        alt: "Public Profile Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 404,
        columnNumber: 5
    }, this);
};
const SellerDashboardIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gauge.svg",
        alt: "Seller Dashboard Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 409,
        columnNumber: 5
    }, this);
};
const MarketplaceDisputeIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gavel.svg",
        alt: "Marketplace Dispute Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 414,
        columnNumber: 5
    }, this);
};
const MarketplaceListIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-marketplace-icon.svg",
        alt: "Marketplace List Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 419,
        columnNumber: 5
    }, this);
};
const PurchasedProductIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-package.svg",
        alt: "Purchased Product Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 424,
        columnNumber: 5
    }, this);
};
const SoldProductIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-coin.svg",
        alt: "Sold Product Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 429,
        columnNumber: 5
    }, this);
};
const LogoutIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "18",
        height: "18",
        viewBox: "0 0 18 18",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M2 18C1.45 18 0.979333 17.8043 0.588 17.413C0.196667 17.0217 0.000666667 16.5507 0 16V2C0 1.45 0.196 0.979333 0.588 0.588C0.98 0.196667 1.45067 0.000666667 2 0H9V2H2V16H9V18H2ZM13 14L11.625 12.55L14.175 10H6V8H14.175L11.625 5.45L13 4L18 9L13 14Z",
            fill: "#00ADEF"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 441,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 434,
        columnNumber: 5
    }, this);
};
const CheckGradientIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "211",
        height: "208",
        viewBox: "0 0 211 208",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M203.341 74.5632C206.051 83.8216 207.504 93.6158 207.504 103.752C207.504 161.052 161.052 207.504 103.752 207.504C46.4519 207.504 0 161.052 0 103.752C0 46.4518 46.4519 -9.15527e-05 103.752 -9.15527e-05C131.892 -9.15527e-05 157.416 11.2021 176.105 29.3936L194.876 22.6914L101.952 115.617L63.9188 77.5842L39.9479 101.556L101.952 163.559L210.127 55.3835L203.341 74.5632Z",
                fill: "url(#paint0_radial_490_1337)"
            }, void 0, false, {
                fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                lineNumber: 457,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("radialGradient", {
                    id: "paint0_radial_490_1337",
                    cx: "0",
                    cy: "0",
                    r: "1",
                    gradientUnits: "userSpaceOnUse",
                    gradientTransform: "translate(59.8666 54.0097) rotate(45) scale(197.976)",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                            stopColor: "#73D1E1"
                        }, void 0, false, {
                            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                            lineNumber: 470,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                            offset: "1",
                            stopColor: "#395BB2"
                        }, void 0, false, {
                            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                            lineNumber: 471,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                    lineNumber: 462,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                lineNumber: 461,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 450,
        columnNumber: 5
    }, this);
};
const CalculatorIcon = ()=>{
    return(// <svg
    //   width="38"
    //   height="38"
    //   viewBox="0 0 38 38"
    //   fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
    // >
    //   <path
    //     d="M10.6667 31.5H13.7917V27.3333H17.9583V24.2083H13.7917V20.0417H10.6667V24.2083H6.5V27.3333H10.6667V31.5ZM21.0833 29.9375H31.5V26.8125H21.0833V29.9375ZM21.0833 24.7292H31.5V21.6042H21.0833V24.7292ZM23.375 16.8125L26.2917 13.8958L29.2083 16.8125L31.3958 14.625L28.4792 11.6042L31.3958 8.6875L29.2083 6.5L26.2917 9.41667L23.375 6.5L21.1875 8.6875L24.1042 11.6042L21.1875 14.625L23.375 16.8125ZM7.02084 13.1667H17.4375V10.0417H7.02084V13.1667ZM4.41667 37.75C3.27084 37.75 2.28959 37.3424 1.47292 36.5271C0.656253 35.7118 0.248615 34.7306 0.250004 33.5833V4.41667C0.250004 3.27083 0.658337 2.29028 1.475 1.475C2.29167 0.659722 3.27223 0.251389 4.41667 0.25H33.5833C34.7292 0.25 35.7104 0.658333 36.5271 1.475C37.3438 2.29167 37.7514 3.27222 37.75 4.41667V33.5833C37.75 34.7292 37.3424 35.7104 36.5271 36.5271C35.7118 37.3438 34.7306 37.7514 33.5833 37.75H4.41667Z"
    //     fill="#00ADEF"
    //   />
    // </svg>
    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-calculator.svg",
        alt: ""
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 491,
        columnNumber: 5
    }, this));
};
const SolidRedArrowIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "31",
        height: "36",
        viewBox: "0 0 31 36",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M28.5 13.6699C31.8333 15.5944 31.8333 20.4056 28.5 22.3301L7.5 34.4545C4.16666 36.379 -1.74729e-06 33.9734 -1.57905e-06 30.1243L-5.19101e-07 5.87564C-3.50856e-07 2.02664 4.16667 -0.378984 7.5 1.54552L28.5 13.6699Z",
            fill: "#FF696A"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 503,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 496,
        columnNumber: 5
    }, this);
};
const DropArrowIcon = ({ width = 24, height = 13 })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: width,
        height: height,
        viewBox: "0 0 24 13",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M3.47599 3.12261e-08L0.857422 2.47368L12.0003 13L23.1431 2.47368L20.5246 2.34528e-07L12.0003 8.03509L3.47599 3.12261e-08Z",
            fill: "white"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 519,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 512,
        columnNumber: 5
    }, this);
};
const DropArrowUpIcon = ({ width = 24, height = 13 })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: width,
        height: height,
        viewBox: "0 0 24 13",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        style: {
            transform: 'rotate(180deg)'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M3.47599 3.12261e-08L0.857422 2.47368L12.0003 13L23.1431 2.47368L20.5246 2.34528e-07L12.0003 8.03509L3.47599 3.12261e-08Z",
            fill: "white"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 536,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 528,
        columnNumber: 5
    }, this);
};
const DropDownArrowIcon = ({ width = 14, height = 7 })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: width,
        height: height,
        viewBox: "0 0 14 7",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M0.333008 0.333984L6.99967 7.00065L13.6663 0.333984H0.333008Z",
            fill: "white"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 550,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 545,
        columnNumber: 5
    }, this);
};
const BlackDropDownArrowIcon = ({ width = 14, height = 7 })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: width,
        height: height,
        viewBox: "0 0 14 7",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M0.333252 0.333008L6.99992 6.99967L13.6666 0.333008H0.333252Z",
            fill: "#666666"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 560,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 557,
        columnNumber: 5
    }, this);
};
const DeleteIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-delete.svg",
        alt: "Delete Icon",
        width: "20",
        height: "21"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 567,
        columnNumber: 5
    }, this);
};
const TradeIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "48",
        height: "48",
        viewBox: "0 0 48 48",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M0.75 42.0833H47.25V47.25H0.75V42.0833ZM8.5 34.3333C11.3417 34.3333 13.6667 32.0083 13.6667 29.1667C13.6667 27.875 13.15 26.5833 12.375 25.8083L15.7333 18.8333H16.25C17.5417 18.8333 18.8333 18.3167 19.6083 17.5417L26.5833 21.1583V21.4167C26.5833 24.2583 28.9083 26.5833 31.75 26.5833C34.5917 26.5833 36.9167 24.2583 36.9167 21.4167C36.9167 20.125 36.4 19.0917 35.625 18.0583L38.9833 11.0833H39.5C42.3417 11.0833 44.6667 8.75833 44.6667 5.91667C44.6667 3.075 42.3417 0.75 39.5 0.75C36.6583 0.75 34.3333 3.075 34.3333 5.91667C34.3333 7.20833 34.85 8.5 35.625 9.275L32.2667 16.25H31.75C30.4583 16.25 29.1667 16.7667 28.3917 17.5417L21.4167 14.1833V13.6667C21.4167 10.825 19.0917 8.5 16.25 8.5C13.4083 8.5 11.0833 10.825 11.0833 13.6667C11.0833 14.9583 11.6 16.25 12.375 17.025L9.01667 24H8.5C5.65833 24 3.33333 26.325 3.33333 29.1667C3.33333 32.0083 5.65833 34.3333 8.5 34.3333Z",
            fill: "#00ADEF"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 582,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 575,
        columnNumber: 5
    }, this);
};
const ArtArrowIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "30",
        height: "32",
        viewBox: "0 0 30 32",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M1.64673 22.9062L27.6275 7.90625",
                stroke: "#00ADEF",
                strokeWidth: "3",
                strokeLinecap: "round"
            }, void 0, false, {
                fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                lineNumber: 598,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M1.64673 15.0469L8.64673 8.04688",
                stroke: "#00ADEF",
                strokeWidth: "3",
                strokeLinecap: "round"
            }, void 0, false, {
                fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                lineNumber: 604,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M3.14673 29.0469H13.0462",
                stroke: "#00ADEF",
                strokeWidth: "3",
                strokeLinecap: "round"
            }, void 0, false, {
                fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                lineNumber: 610,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 591,
        columnNumber: 5
    }, this);
};
const RedInfoIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-warning.svg",
        alt: "Red Info Icon",
        width: "28",
        height: "28"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 621,
        columnNumber: 5
    }, this);
};
const GreyCheckIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-success-gray.svg",
        alt: "Grey Success Icon",
        width: "28",
        height: "28"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 629,
        columnNumber: 5
    }, this);
};
const GreyCrossIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-failure.svg",
        alt: "Grey Cross Icon",
        width: "28",
        height: "28"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 637,
        columnNumber: 5
    }, this);
};
const ReferIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-refer-a-friend.svg",
        alt: "Refer Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 645,
        columnNumber: 5
    }, this);
};
const PartnershipIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        id: "Layer_1",
        "data-name": "Layer 1",
        xmlns: "http://www.w3.org/2000/svg",
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "m17.063,2.185c-1.245.06-2.442.603-3.367,1.528l-3.63,3.63c.57-.573,2.687-.179,3.2.334l2.197-2.197c.487-.487,1.096-.785,1.719-.812.424-.021,1.024.069,1.552.597.493.493.597,1.066.597,1.457,0,.654-.299,1.304-.812,1.815l-3.821,3.845c-.961.961-2.424,1.039-3.272.191-.484-.484-1.281-.487-1.767,0s-.487,1.281,0,1.767c.872.872,2.018,1.313,3.2,1.313,1.278,0,2.582-.522,3.582-1.528l3.845-3.821c.976-.973,1.528-2.275,1.528-3.582,0-1.215-.46-2.37-1.313-3.224-.913-.913-2.14-1.373-3.439-1.313Zm-5.922,6.161c-1.278,0-2.603.525-3.606,1.528l-3.821,3.821c-.976.973-1.528,2.275-1.528,3.582,0,1.215.46,2.37,1.313,3.224.913.913,2.14,1.373,3.439,1.313,1.245-.06,2.442-.603,3.367-1.528l3.63-3.63c-.573.573-2.687.179-3.2-.334l-2.197,2.197c-.487.487-1.096.782-1.719.812-.424.021-1.024-.069-1.552-.597-.493-.493-.597-1.069-.597-1.457,0-.654.299-1.304.812-1.815l3.821-3.845c.961-.961,2.424-1.036,3.272-.191.487.487,1.284.487,1.767,0,.487-.487.487-1.281,0-1.767-.872-.872-2.021-1.313-3.2-1.313Z",
            fill: "#fff"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 658,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 650,
        columnNumber: 5
    }, this);
};
const ResendCodeIcon = ({ isRotating })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        className: isRotating ? "rotate" : "",
        width: "19",
        height: "18",
        viewBox: "0 0 19 18",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M18.7693 1.84688V6.34688C18.7693 6.54579 18.6903 6.73656 18.5497 6.87721C18.409 7.01786 18.2182 7.09688 18.0193 7.09688H13.5193C13.3717 7.09664 13.2275 7.05285 13.1047 6.97101C12.9818 6.88917 12.8859 6.77291 12.8289 6.63679C12.7718 6.50066 12.7562 6.35074 12.784 6.20578C12.8117 6.06082 12.8816 5.92728 12.985 5.82188L14.71 4.09688L14.3068 3.69375C13.2576 2.64569 11.9212 1.93222 10.4666 1.6435C9.01196 1.35479 7.50438 1.5038 6.13442 2.07171C4.76446 2.63961 3.59362 3.60091 2.76988 4.83409C1.94613 6.06728 1.50648 7.517 1.50648 9C1.50648 10.483 1.94613 11.9327 2.76988 13.1659C3.59362 14.3991 4.76446 15.3604 6.13442 15.9283C7.50438 16.4962 9.01196 16.6452 10.4666 16.3565C11.9212 16.0678 13.2576 15.3543 14.3068 14.3063C14.3758 14.2357 14.4582 14.1796 14.5492 14.1413C14.6401 14.103 14.7378 14.0833 14.8365 14.0833C14.9352 14.0833 15.0329 14.103 15.1239 14.1413C15.2148 14.1796 15.2972 14.2357 15.3662 14.3063C15.5065 14.4469 15.5852 14.6373 15.5852 14.8359C15.5852 15.0345 15.5065 15.225 15.3662 15.3656C14.1073 16.6238 12.5037 17.4805 10.758 17.8274C9.01229 18.1743 7.20293 17.9958 5.55867 17.3145C3.91441 16.6331 2.50908 15.4796 1.52035 13.9996C0.531628 12.5197 0.00390625 10.7798 0.00390625 9C0.00390625 7.22017 0.531628 5.4803 1.52035 4.00036C2.50908 2.52042 3.91441 1.36687 5.55867 0.685539C7.20293 0.00421169 9.01229 -0.174293 10.758 0.172592C12.5037 0.519478 14.1073 1.37618 15.3662 2.63438L15.7693 3.0375L17.485 1.32188C17.589 1.21629 17.722 1.14391 17.8672 1.11388C18.0124 1.08384 18.1632 1.0975 18.3006 1.15313C18.4374 1.21109 18.5545 1.30747 18.6377 1.43059C18.7209 1.55371 18.7666 1.69831 18.7693 1.84688Z",
            fill: "white"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 675,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 667,
        columnNumber: 5
    }, this);
};
const ContactCustomerSupport = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "18",
        height: "18",
        viewBox: "0 0 18 18",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M9 0.875C7.39303 0.875 5.82214 1.35152 4.486 2.24431C3.14985 3.1371 2.10844 4.40605 1.49348 5.8907C0.87852 7.37535 0.717618 9.00901 1.03112 10.5851C1.34463 12.1612 2.11846 13.6089 3.25476 14.7452C4.39106 15.8815 5.8388 16.6554 7.4149 16.9689C8.99099 17.2824 10.6247 17.1215 12.1093 16.5065C13.594 15.8916 14.8629 14.8502 15.7557 13.514C16.6485 12.1779 17.125 10.607 17.125 9C17.1209 6.84638 16.2635 4.78216 14.7407 3.25932C13.2178 1.73648 11.1536 0.87913 9 0.875ZM9 14C8.81458 14 8.63333 13.945 8.47916 13.842C8.32499 13.739 8.20482 13.5926 8.13387 13.4213C8.06291 13.25 8.04434 13.0615 8.08052 12.8796C8.11669 12.6977 8.20598 12.5307 8.33709 12.3996C8.4682 12.2685 8.63525 12.1792 8.81711 12.143C8.99896 12.1068 9.18746 12.1254 9.35877 12.1964C9.53007 12.2673 9.67649 12.3875 9.77951 12.5417C9.88252 12.6958 9.9375 12.8771 9.9375 13.0625C9.9375 13.3111 9.83873 13.5496 9.66292 13.7254C9.4871 13.9012 9.24864 14 9 14ZM9.625 10.1797V10.25C9.625 10.4158 9.55916 10.5747 9.44195 10.6919C9.32474 10.8092 9.16576 10.875 9 10.875C8.83424 10.875 8.67527 10.8092 8.55806 10.6919C8.44085 10.5747 8.375 10.4158 8.375 10.25V9.625C8.375 9.45924 8.44085 9.30027 8.55806 9.18306C8.67527 9.06585 8.83424 9 9 9C9.30904 9 9.61113 8.90836 9.86808 8.73667C10.125 8.56498 10.3253 8.32095 10.4436 8.03544C10.5618 7.74993 10.5928 7.43577 10.5325 7.13267C10.4722 6.82958 10.3234 6.55117 10.1049 6.33265C9.88634 6.11413 9.60793 5.96531 9.30483 5.90502C9.00174 5.84473 8.68757 5.87568 8.40206 5.99394C8.11655 6.1122 7.87252 6.31247 7.70083 6.56942C7.52914 6.82637 7.4375 7.12847 7.4375 7.4375C7.4375 7.60326 7.37166 7.76223 7.25445 7.87944C7.13724 7.99665 6.97826 8.0625 6.8125 8.0625C6.64674 8.0625 6.48777 7.99665 6.37056 7.87944C6.25335 7.76223 6.1875 7.60326 6.1875 7.4375C6.18751 6.90805 6.33695 6.38936 6.61865 5.94107C6.90036 5.49279 7.30287 5.13312 7.77991 4.90344C8.25694 4.67376 8.78912 4.58339 9.31523 4.64273C9.84134 4.70207 10.34 4.90871 10.7539 5.23888C11.1678 5.56905 11.4801 6.00934 11.6549 6.50911C11.8296 7.00888 11.8598 7.54784 11.7418 8.06398C11.6239 8.58013 11.3627 9.05251 10.9882 9.42678C10.6137 9.80106 10.1412 10.062 9.625 10.1797Z",
            fill: "#00ADEF"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 691,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 684,
        columnNumber: 5
    }, this);
};
const RedErrorCircle = ()=>{
    return(// <svg
    //   xmlns="http://www.w3.org/2000/svg"
    //   width="24"
    //   height="24"
    //   viewBox="0 0 24 24"
    //   fill="#ff696a"
    // >
    //   <path
    //     d="M11.953 2C6.465 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.493 2 11.953 2zM13 17h-2v-2h2v2zm0-4h-2V7h2v6z"
    //     fill="#fffff"
    //   />
    // </svg>
    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-warning.svg",
        alt: "Red Error Icon",
        width: "24",
        height: "24"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 714,
        columnNumber: 5
    }, this));
};
const BlackErrorCircle = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "18",
        height: "17",
        viewBox: "0 0 18 17",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M9 0.375C9 0.375 10.6526 0.375 12.1628 1.01376C12.1628 1.01376 13.621 1.63053 14.7452 2.75476C14.7452 2.75476 15.8695 3.87899 16.4862 5.33719C16.4862 5.33719 17.125 6.84739 17.125 8.5C17.125 8.5 17.125 10.1526 16.4862 11.6628C16.4862 11.6628 15.8695 13.121 14.7452 14.2452C14.7452 14.2452 13.621 15.3695 12.1628 15.9862C12.1628 15.9862 10.6526 16.625 9 16.625C9 16.625 7.34739 16.625 5.83719 15.9862C5.83719 15.9862 4.37899 15.3695 3.25476 14.2452C3.25476 14.2452 2.13053 13.121 1.51376 11.6628C1.51376 11.6628 0.875 10.1526 0.875 8.5C0.875 8.5 0.875 6.84739 1.51376 5.33719C1.51376 5.33719 2.13053 3.87899 3.25476 2.75476C3.25476 2.75476 4.37899 1.63053 5.83719 1.01376C5.83719 1.01376 7.34739 0.375 9 0.375ZM9 1.625C9 1.625 7.60087 1.625 6.32413 2.16501C6.32413 2.16501 5.09047 2.68681 4.13864 3.63864C4.13864 3.63864 3.18681 4.59047 2.66502 5.82413C2.66502 5.82413 2.125 7.10087 2.125 8.5C2.125 8.5 2.125 9.89913 2.66502 11.1759C2.66502 11.1759 3.18681 12.4095 4.13864 13.3614C4.13864 13.3614 5.09047 14.3132 6.32413 14.835C6.32413 14.835 7.60087 15.375 9 15.375C9 15.375 10.3991 15.375 11.6759 14.835C11.6759 14.835 12.9095 14.3132 13.8614 13.3614C13.8614 13.3614 14.8132 12.4095 15.335 11.1759C15.335 11.1759 15.875 9.89912 15.875 8.5C15.875 8.5 15.875 7.10087 15.335 5.82413C15.335 5.82413 14.8132 4.59047 13.8614 3.63864C13.8614 3.63864 12.9095 2.68681 11.6759 2.16501C11.6759 2.16501 10.3991 1.625 9 1.625Z",
                fill: "#1C1C1C"
            }, void 0, false, {
                fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                lineNumber: 730,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M9 12.875H9.625C9.97018 12.875 10.25 12.5952 10.25 12.25C10.25 11.9048 9.97018 11.625 9.625 11.625V7.875C9.625 7.52982 9.34518 7.25 9 7.25H8.375C8.02982 7.25 7.75 7.52982 7.75 7.875C7.75 8.22018 8.02982 8.5 8.375 8.5V12.25C8.375 12.5952 8.65482 12.875 9 12.875Z",
                fill: "#1C1C1C"
            }, void 0, false, {
                fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                lineNumber: 736,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M9.78125 5.0625C9.78125 5.58027 9.36148 6 8.84375 6C8.32602 6 7.90625 5.58027 7.90625 5.0625C7.90625 4.54473 8.32602 4.125 8.84375 4.125C9.36148 4.125 9.78125 4.54473 9.78125 5.0625Z",
                fill: "#1C1C1C"
            }, void 0, false, {
                fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                lineNumber: 740,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 723,
        columnNumber: 5
    }, this);
};
const DeviceMobileSpeaker = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-device-00ADEF.svg",
        alt: "Device Mobile Speaker Icon",
        width: "22",
        height: "22"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 750,
        columnNumber: 5
    }, this);
};
const ViewCartBaseBlue = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-00ADEF.svg",
        alt: "View Cart BaseBlue"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 759,
        columnNumber: 5
    }, this);
};
const ViewCartDarkBlue = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-04498C.svg",
        alt: "View Cart Dark Blue"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 764,
        columnNumber: 5
    }, this);
};
const ViewCartGray = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-808080.svg",
        alt: "View Cart Gray"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 770,
        columnNumber: 5
    }, this);
};
const CheckoutCardBaseBlue = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-00ADEF.svg",
        alt: "Checkout Card BaseBlue"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 776,
        columnNumber: 5
    }, this);
};
const CheckoutCardDarkBlue = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-04498C.svg",
        alt: "Checkout Card BaseBlue"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 781,
        columnNumber: 5
    }, this);
};
const CheckoutCardGray = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-808080.svg",
        alt: "Checkout Card Gray"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 786,
        columnNumber: 5
    }, this);
};
const AccessBaseBlue = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-00ADEF.svg",
        alt: "Access Base Blue"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 791,
        columnNumber: 5
    }, this);
};
const AccessDarkBlue = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-04498C.svg",
        alt: "Access Dark Blue"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 796,
        columnNumber: 5
    }, this);
};
const AccessGray = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-808080.svg",
        alt: "Access Gray"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 801,
        columnNumber: 5
    }, this);
};
const TopRightArrowIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "13",
        height: "13",
        viewBox: "0 0 13 13",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M0.832189 10.79L9.12219 2.5H3.54219C3.27697 2.5 3.02262 2.39464 2.83508 2.20711C2.64754 2.01957 2.54219 1.76522 2.54219 1.5C2.54219 1.23478 2.64754 0.98043 2.83508 0.792893C3.02262 0.605357 3.27697 0.5 3.54219 0.5H11.4922C11.7574 0.5 12.0118 0.605357 12.1993 0.792893C12.3868 0.98043 12.4922 1.23478 12.4922 1.5V9.5C12.4922 9.76522 12.3868 10.0196 12.1993 10.2071C12.0118 10.3946 11.7574 10.5 11.4922 10.5H11.5422C11.277 10.5 11.0226 10.3946 10.8351 10.2071C10.6475 10.0196 10.5422 9.76522 10.5422 9.5V3.95L2.28219 12.21C2.18922 12.3037 2.07862 12.3781 1.95677 12.4289C1.83491 12.4797 1.7042 12.5058 1.57219 12.5058C1.44018 12.5058 1.30947 12.4797 1.18761 12.4289C1.06575 12.3781 0.955151 12.3037 0.862187 12.21C0.766468 12.119 0.689713 12.01 0.636353 11.8892C0.582994 11.7684 0.554085 11.6383 0.551295 11.5063C0.548506 11.3742 0.571892 11.243 0.620103 11.12C0.668314 10.9971 0.740396 10.8849 0.832189 10.79Z",
            fill: "#00ADEF"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 813,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 806,
        columnNumber: 5
    }, this);
};
const MoneyWithWings = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-money-wings.svg",
        alt: "Money Wings Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 822,
        columnNumber: 5
    }, this);
};
const FlatBlueBook = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-book-00A6ED.svg",
        alt: "Flat Blue Book"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 827,
        columnNumber: 5
    }, this);
};
const RedCircleCrossIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-failure-red.svg",
        alt: "Red Cross"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 833,
        columnNumber: 5
    }, this);
};
const WhiteCrossCircle = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "28",
        height: "28",
        viewBox: "0 0 28 28",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M14 0C6.2 0 0 6.2 0 14C0 21.8 6.2 28 14 28C21.8 28 28 21.8 28 14C28 6.2 21.8 0 14 0ZM19.4 21L14 15.6L8.6 21L7 19.4L12.4 14L7 8.6L8.6 7L14 12.4L19.4 7L21 8.6L15.6 14L21 19.4L19.4 21Z",
            fill: "white"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 840,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 839,
        columnNumber: 5
    }, this);
};
const BlackCross = ({ width = 14, height = 14 })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: width,
        height: height,
        viewBox: "0 0 14 14",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M1.4 14L0 12.6L5.6 7L0 1.4L1.4 0L7 5.6L12.6 0L14 1.4L8.4 7L14 12.6L12.6 14L7 8.4L1.4 14Z",
            fill: "black"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 848,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 847,
        columnNumber: 5
    }, this);
};
const AddPlusIcon = ({ width = 10, height = 10 })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg",
        width: width,
        height: height,
        alt: "Add Model Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 856,
        columnNumber: 5
    }, this);
};
const DragDropIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drag-drop-icon.svg",
        alt: "Drap Drop Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 862,
        columnNumber: 5
    }, this);
};
const SolidIncon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-marker.svg",
        alt: "Info Marker Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 868,
        columnNumber: 5
    }, this);
};
const BlackDownIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-black-down-arrow.svg",
        alt: "Info Marker Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 873,
        columnNumber: 5
    }, this);
};
const YellowInfoHexa = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "14",
        viewBox: "0 0 12 14",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            fillRule: "evenodd",
            clipRule: "evenodd",
            d: "M3.22867 1.53727C4.58133 0.736604 5.25733 0.335938 6 0.335938C6.74267 0.335938 7.41867 0.735938 8.77133 1.53727L9.22867 1.80794C10.5813 2.60927 11.2573 3.00994 11.6287 3.66927C12 4.32927 12 5.12927 12 6.73194V7.27327C12 8.87527 12 9.6766 11.6287 10.3359C11.2573 10.9959 10.5813 11.3959 9.22867 12.1966L8.77133 12.4679C7.41867 13.2686 6.74267 13.6693 6 13.6693C5.25733 13.6693 4.58133 13.2693 3.22867 12.4679L2.77133 12.1966C1.41867 11.3966 0.742667 10.9953 0.371333 10.3359C-3.97364e-08 9.67594 0 8.87594 0 7.27327V6.73194C0 5.12927 -3.97364e-08 4.3286 0.371333 3.66927C0.742667 3.00927 1.41867 2.60927 2.77133 1.80794L3.22867 1.53727ZM6.66667 9.66927C6.66667 9.84608 6.59643 10.0157 6.4714 10.1407C6.34638 10.2657 6.17681 10.3359 6 10.3359C5.82319 10.3359 5.65362 10.2657 5.5286 10.1407C5.40357 10.0157 5.33333 9.84608 5.33333 9.66927C5.33333 9.49246 5.40357 9.32289 5.5286 9.19787C5.65362 9.07284 5.82319 9.0026 6 9.0026C6.17681 9.0026 6.34638 9.07284 6.4714 9.19787C6.59643 9.32289 6.66667 9.49246 6.66667 9.66927ZM6 3.16927C6.13261 3.16927 6.25979 3.22195 6.35355 3.31572C6.44732 3.40949 6.5 3.53666 6.5 3.66927V7.66927C6.5 7.80188 6.44732 7.92906 6.35355 8.02282C6.25979 8.11659 6.13261 8.16927 6 8.16927C5.86739 8.16927 5.74022 8.11659 5.64645 8.02282C5.55268 7.92906 5.5 7.80188 5.5 7.66927V3.66927C5.5 3.53666 5.55268 3.40949 5.64645 3.31572C5.74022 3.22195 5.86739 3.16927 6 3.16927Z",
            fill: "#FEA500"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 886,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 879,
        columnNumber: 5
    }, this);
};
const TripleDotsMenu = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-kebab-menu.svg",
        alt: "Kabab Menu"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 894,
        columnNumber: 5
    }, this);
};
const WhiteDownArrow = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "13",
        height: "6",
        viewBox: "0 0 14 8",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M0.666504 0.667969L7.33317 7.33463L13.9998 0.667969H0.666504Z",
            fill: "white"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 901,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 900,
        columnNumber: 5
    }, this);
};
const BlackDownArrow = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "13",
        height: "6",
        viewBox: "0 0 14 8",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M0.666504 0.667969L7.33317 7.33463L13.9998 0.667969H0.666504Z",
            fill: "black"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 909,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 908,
        columnNumber: 5
    }, this);
};
const WhiteCrossIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "16",
        height: "15",
        viewBox: "0 0 16 15",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M9.05969 7.49973L15.2847 1.2841C15.4043 1.13838 15.4654 0.953383 15.4562 0.765094C15.4469 0.576804 15.368 0.398686 15.2347 0.265385C15.1014 0.132084 14.9232 0.0531312 14.7349 0.0438836C14.5467 0.0346361 14.3617 0.095755 14.2159 0.215352L8.00031 6.44035L1.78469 0.215352C1.63897 0.095755 1.45397 0.0346361 1.26568 0.0438836C1.07739 0.0531312 0.899272 0.132084 0.76597 0.265385C0.632669 0.398686 0.553717 0.576804 0.544469 0.765094C0.535221 0.953383 0.59634 1.13838 0.715938 1.2841L6.94094 7.49973L0.715938 13.7154C0.575101 13.8575 0.496094 14.0496 0.496094 14.2497C0.496094 14.4499 0.575101 14.6419 0.715938 14.7841C0.859293 14.9227 1.0509 15.0002 1.25031 15.0002C1.44972 15.0002 1.64133 14.9227 1.78469 14.7841L8.00031 8.5591L14.2159 14.7841C14.3593 14.9227 14.5509 15.0002 14.7503 15.0002C14.9497 15.0002 15.1413 14.9227 15.2847 14.7841C15.4255 14.6419 15.5045 14.4499 15.5045 14.2497C15.5045 14.0496 15.4255 13.8575 15.2847 13.7154L9.05969 7.49973Z",
            fill: "white"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 916,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 915,
        columnNumber: 5
    }, this);
};
const ProfileUserDarkIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-profile-user-dark.svg",
        alt: "Profile Dark Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 925,
        columnNumber: 5
    }, this);
};
const BlueLocationIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-location.svg",
        alt: "Blue Location Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 930,
        columnNumber: 5
    }, this);
};
const RatingStarIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-star-single.svg",
        alt: "Rating Star Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 935,
        columnNumber: 5
    }, this);
};
const ThumbUpIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-thumb-up-green.svg",
        alt: "Thumb Up Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 940,
        columnNumber: 5
    }, this);
};
const ThumbDownIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-thumb-down-red.svg",
        alt: "Thumb Down Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 945,
        columnNumber: 5
    }, this);
};
const BlackShareIcon = ({ width = 18, height = 13 })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-share-icon.svg",
        width: width,
        height: height,
        alt: "Black Share Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 950,
        columnNumber: 5
    }, this);
};
const StaticListingImg = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png",
        alt: "Black Share Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 955,
        columnNumber: 5
    }, this);
};
const WhiteDropDownArrow = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drop-arrow.svg",
        alt: "White DropDown Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 961,
        columnNumber: 5
    }, this);
};
const WhiteSingleStack = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-single-stack.svg",
        alt: "White Single Stack Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 966,
        columnNumber: 5
    }, this);
};
const WhiteDoubleStack = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-double-stack.svg",
        alt: "White Double Stack Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 971,
        columnNumber: 5
    }, this);
};
const WhiteTripleStack = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-triple-stack.svg",
        alt: "White Triple Stack Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 976,
        columnNumber: 5
    }, this);
};
const OpenNewtabIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-open-new-tab.svg",
        alt: "New Tab Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 981,
        columnNumber: 5
    }, this);
};
const RenameIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-edit-pencil.svg",
        alt: "Rename Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 986,
        columnNumber: 5
    }, this);
};
const DeleteDarkIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trashcan-dark.svg",
        alt: "Delete Dark Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 991,
        columnNumber: 5
    }, this);
};
const EyeDarkInsightIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-dark-insights.svg",
        alt: "Eye Dark Insight Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 996,
        columnNumber: 5
    }, this);
};
const EyeDarkIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-dark.svg",
        alt: "Eye Dark Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1001,
        columnNumber: 5
    }, this);
};
const FollowersIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-followers-icon.svg",
        alt: "Followers Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1006,
        columnNumber: 5
    }, this);
};
const ShareLightStrIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-share-white-fill.svg",
        alt: "Followers Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1011,
        columnNumber: 5
    }, this);
};
const RelistIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-relist-icon.svg",
        alt: "Relist Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1016,
        columnNumber: 5
    }, this);
};
const DigitaLAssetIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-digital-asset.svg",
        alt: "Digital Asset Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1021,
        columnNumber: 5
    }, this);
};
const LicenseIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-license.svg",
        alt: "license Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1026,
        columnNumber: 5
    }, this);
};
const LightEyeIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-light.svg",
        alt: "Light Eye Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1031,
        columnNumber: 5
    }, this);
};
const AddBlueIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-add.svg",
        alt: "Add Blue Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1036,
        columnNumber: 5
    }, this);
};
const PinIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-pin.svg",
        alt: "Pin Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1041,
        columnNumber: 5
    }, this);
};
const RightArrowIconSvg = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "8",
        height: "13",
        viewBox: "0 0 8 13",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            fillRule: "evenodd",
            clipRule: "evenodd",
            d: "M7.15694 7.21102L1.49994 12.868L0.0859375 11.454L5.03594 6.50401L0.0859375 1.55401L1.49994 0.140015L7.15694 5.79701C7.34441 5.98454 7.44972 6.23885 7.44972 6.50401C7.44972 6.76918 7.34441 7.02349 7.15694 7.21102Z",
            fill: "white"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 1054,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1047,
        columnNumber: 5
    }, this);
};
const EditIconSvg = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "18",
        height: "18",
        viewBox: "0 0 18 18",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M17.71 4.04C18.1 3.65 18.1 3 17.71 2.63L15.37 0.289999C15 -0.100001 14.35 -0.100001 13.96 0.289999L12.12 2.12L15.87 5.87M0 14.25V18H3.75L14.81 6.93L11.06 3.18L0 14.25Z",
            fill: "#00ADEF"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 1071,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1064,
        columnNumber: 5
    }, this);
};
const PlusIconSvg = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "33",
        height: "32",
        viewBox: "0 0 33 32",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M32.5 20H20.5V32H12.5V20H0.5V12H12.5V0H20.5V12H32.5V20Z",
            fill: "white"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 1085,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1078,
        columnNumber: 5
    }, this);
};
const RemoveIconSvg = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "18",
        height: "19",
        viewBox: "0 0 18 19",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M9 0.75C4.125 0.75 0.25 4.625 0.25 9.5C0.25 14.375 4.125 18.25 9 18.25C13.875 18.25 17.75 14.375 17.75 9.5C17.75 4.625 13.875 0.75 9 0.75ZM12.375 13.875L9 10.5L5.625 13.875L4.625 12.875L8 9.5L4.625 6.125L5.625 5.125L9 8.5L12.375 5.125L13.375 6.125L10 9.5L13.375 12.875L12.375 13.875Z",
            fill: "white"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 1100,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1093,
        columnNumber: 5
    }, this);
};
const RightSolidArrowIconSvg = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "13",
        viewBox: "0 0 12 13",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M10.9903 5.63999L1.94025 0.769994C1.18025 0.359994 0.300251 1.06999 0.540251 1.89999L1.78025 6.23999C1.83025 6.41999 1.83025 6.59999 1.78025 6.77999L0.540251 11.12C0.300251 11.95 1.18025 12.66 1.94025 12.25L10.9903 7.37999C11.1446 7.29563 11.2735 7.17127 11.3632 7.01995C11.453 6.86862 11.5004 6.69593 11.5004 6.51999C11.5004 6.34406 11.453 6.17136 11.3632 6.02004C11.2735 5.86872 11.1446 5.74435 10.9903 5.65999V5.63999Z",
            fill: "#00ADEF"
        }, void 0, false, {
            fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
            lineNumber: 1116,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1109,
        columnNumber: 5
    }, this);
};
const LocationIconSvg = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M10.0004 11.1912C11.4363 11.1912 12.6004 10.0272 12.6004 8.59121C12.6004 7.15527 11.4363 5.99121 10.0004 5.99121C8.56445 5.99121 7.40039 7.15527 7.40039 8.59121C7.40039 10.0272 8.56445 11.1912 10.0004 11.1912Z",
                stroke: "#292D32",
                "stroke-width": "1.5"
            }, void 0, false, {
                fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                lineNumber: 1133,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M3.01675 7.07533C4.65842 -0.141339 15.3501 -0.133006 16.9834 7.08366C17.9417 11.317 15.3084 14.9003 13.0001 17.117C11.3251 18.7337 8.67508 18.7337 6.99175 17.117C4.69175 14.9003 2.05842 11.3087 3.01675 7.07533Z",
                stroke: "#292D32",
                strokeWidth: "1.5"
            }, void 0, false, {
                fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
                lineNumber: 1138,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1126,
        columnNumber: 5
    }, this);
};
const BulletPointIcon = ({ width = 8, height = 8 })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-bullet-point.svg",
        width: width,
        height: height,
        alt: "Bullet Point Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1150,
        columnNumber: 5
    }, this);
};
const CoinWhiteIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-coins-white.svg",
        alt: "White Coin Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1155,
        columnNumber: 5
    }, this);
};
const ProductFormatWhiteIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-product-format-white.svg",
        alt: "Product Format White Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1160,
        columnNumber: 5
    }, this);
};
const GuageWhiteIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gauge-white.svg",
        alt: "Guage White Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1165,
        columnNumber: 5
    }, this);
};
const TradingPlatformWhiteIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trading-platform-white.svg",
        alt: "Trading Platform White Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1170,
        columnNumber: 5
    }, this);
};
const ShuffleWhiteIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-shuffle-white.svg",
        alt: "Shuffle White Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1175,
        columnNumber: 5
    }, this);
};
const ClockWhiteIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-clock-white.svg",
        alt: "Clock White Icon"
    }, void 0, false, {
        fileName: "[project]/src/assets/svgIcons/SvgIcon.js",
        lineNumber: 1180,
        columnNumber: 5
    }, this);
};
}}),
"[project]/src/Components/UI/CommonSearch.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/assets/svgIcons/SvgIcon.js [app-ssr] (ecmascript)");
'use client';
;
;
;
;
const CommonSearch = (props)=>{
    const [inputValue, setInputValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    // Keep in sync with external value when it changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (typeof props.value === "string") {
            setInputValue(props.value);
        }
    }, [
        props.value,
        props.reset
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            props.label && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                className: "form-label",
                htmlFor: props.name,
                children: props.label
            }, void 0, false, {
                fileName: "[project]/src/Components/UI/CommonSearch.jsx",
                lineNumber: 20,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `commonSearch ${props.className}`,
                children: [
                    props?.icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "onlyIcon",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SearchIcons"], {}, void 0, false, {
                            fileName: "[project]/src/Components/UI/CommonSearch.jsx",
                            lineNumber: 27,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/CommonSearch.jsx",
                        lineNumber: 26,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        type: "text",
                        color: "white",
                        id: `${props.name}-id`,
                        name: props.name,
                        placeholder: props?.placeholder,
                        className: `form-control ${props.searcClass}`,
                        value: inputValue,
                        // onChange={(e) => {
                        //   setInputValue(e.target.value);      // instant update for UX
                        //   props.onChange?.(e);                // notify parent (debounced)
                        // }}
                        onChange: (e)=>{
                            const val = e.target.value; // 👈 assign val here
                            setInputValue(val); // instant update for UX
                            props.onChange?.(e); // notify parent (debounced)
                            if (val === "") {
                                props.onClear?.(); // call onClear when input is emptied
                            }
                        },
                        maxLength: props?.maxLength
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/CommonSearch.jsx",
                        lineNumber: 30,
                        columnNumber: 9
                    }, this),
                    props?.btnicon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "btnIcon",
                        onClick: ()=>{
                            setInputValue(""); // reset local
                            props.onChange?.({
                                target: {
                                    value: ""
                                }
                            }); // reset parent
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SearchIcons"], {}, void 0, false, {
                            fileName: "[project]/src/Components/UI/CommonSearch.jsx",
                            lineNumber: 63,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/CommonSearch.jsx",
                        lineNumber: 54,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/Components/UI/CommonSearch.jsx",
                lineNumber: 24,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = CommonSearch;
}}),
"[project]/src/Components/UI/Select.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$select$2f$dist$2f$react$2d$select$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/react-select/dist/react-select.esm.js [app-ssr] (ecmascript) <locals>");
'use client';
;
;
;
const CustomSelect = ({ className, menuIsOpen, defaultValue, onChange, options, name, isMulti, value, isClearable, onMenuScrollToBottom, placeholder, filterOption, isSearchable, closeMenuOnSelect, error })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "customInput_inner errorMargin",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$select$2f$dist$2f$react$2d$select$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], {
                defaultValue: defaultValue,
                onChange: onChange,
                options: options,
                value: value,
                className: `common_select ${className}`,
                classNamePrefix: "select",
                menuIsOpen: menuIsOpen,
                placeholder: placeholder,
                name: name,
                isMulti: isMulti,
                isClearable: isClearable,
                onMenuScrollToBottom: onMenuScrollToBottom,
                filterOption: filterOption,
                closeMenuOnSelect: closeMenuOnSelect,
                isSearchable: isSearchable
            }, void 0, false, {
                fileName: "[project]/src/Components/UI/Select.jsx",
                lineNumber: 25,
                columnNumber: 7
            }, this),
            error
        ]
    }, void 0, true, {
        fileName: "[project]/src/Components/UI/Select.jsx",
        lineNumber: 24,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = CustomSelect;
}}),
"[project]/src/Components/UI/CustomBreadcrumb.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Breadcrumb$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Breadcrumb$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Breadcrumb.js [app-ssr] (ecmascript) <export default as Breadcrumb>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)"); // Import Next.js Link
'use client';
;
;
;
;
const CustomBreadcrumb = ({ href, linkname, pagename, articlename })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "custom_breadcrumb",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Breadcrumb$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Breadcrumb$3e$__["Breadcrumb"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                        className: "breadcrumb-item home-item",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: "/",
                            children: "Home"
                        }, void 0, false, {
                            fileName: "[project]/src/Components/UI/CustomBreadcrumb.jsx",
                            lineNumber: 15,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/CustomBreadcrumb.jsx",
                        lineNumber: 14,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                        className: "breadcrumb-item secondary_link",
                        children: href ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: href,
                            children: linkname
                        }, void 0, false, {
                            fileName: "[project]/src/Components/UI/CustomBreadcrumb.jsx",
                            lineNumber: 19,
                            columnNumber: 21
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: linkname
                        }, void 0, false, {
                            fileName: "[project]/src/Components/UI/CustomBreadcrumb.jsx",
                            lineNumber: 19,
                            columnNumber: 59
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/CustomBreadcrumb.jsx",
                        lineNumber: 17,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Breadcrumb$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Breadcrumb$3e$__["Breadcrumb"].Item, {
                        active: true,
                        children: pagename
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/CustomBreadcrumb.jsx",
                        lineNumber: 22,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: articlename
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/CustomBreadcrumb.jsx",
                        lineNumber: 24,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/Components/UI/CustomBreadcrumb.jsx",
                lineNumber: 13,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/Components/UI/CustomBreadcrumb.jsx",
            lineNumber: 12,
            columnNumber: 7
        }, this)
    }, void 0, false);
};
const __TURBOPACK__default__export__ = CustomBreadcrumb;
}}),
"[project]/src/Components/UI/CommonButton.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
;
/**COMMON BUTTON WITH DYNAMIC PROPS */ /** COMMON BUTTON WITH DYNAMIC PROPS */ const CommonButton = (props)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        onClick: props?.onClick,
        type: props?.type,
        className: `btn-style ${props.className} ${props.fluid ? "w-100" : ""} ${props.transparent ? "transparent" : ""} ${props.white20 ? "white20" : ""} ${props.whiteBtn ? "white-btn" : ""}`,
        disabled: props?.disabled,
        children: [
            props.onlyIcon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "onlyIcon",
                children: props.onlyIcon
            }, void 0, false, {
                fileName: "[project]/src/Components/UI/CommonButton.jsx",
                lineNumber: 15,
                columnNumber: 26
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "d-flex flex-column align-items-center text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: props.title
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/CommonButton.jsx",
                        lineNumber: 18,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "d-block",
                        children: props.trial
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/CommonButton.jsx",
                        lineNumber: 19,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "d-block",
                        children: props.subtitle
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/CommonButton.jsx",
                        lineNumber: 20,
                        columnNumber: 9
                    }, this),
                    props.innerText && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        style: {
                            fontSize: "0.70em",
                            lineHeight: "1"
                        },
                        children: props.innerText
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/CommonButton.jsx",
                        lineNumber: 22,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/Components/UI/CommonButton.jsx",
                lineNumber: 17,
                columnNumber: 7
            }, this),
            props.btnIcon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                src: props.btnIcon,
                alt: props?.title ? `${props.title} icon` : "Button icon",
                className: ""
            }, void 0, false, {
                fileName: "[project]/src/Components/UI/CommonButton.jsx",
                lineNumber: 27,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/Components/UI/CommonButton.jsx",
        lineNumber: 8,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = CommonButton;
}}),
"[project]/src/Components/UI/NavLink.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>NavLink)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
"use client";
;
;
;
function NavLink({ className = "", children, href, ...props }) {
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const isActive = pathname === href;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        href: href,
        ...props,
        className: `${className} ${isActive ? "active" : ""}`,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/Components/UI/NavLink.jsx",
        lineNumber: 11,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/Components/UI/Header.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Container$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Container.js [app-ssr] (ecmascript) <export default as Container>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Navbar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Navbar$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Navbar.js [app-ssr] (ecmascript) <export default as Navbar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Dropdown$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Dropdown$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Dropdown.js [app-ssr] (ecmascript) <export default as Dropdown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$context$2f$LanguageContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/context/LanguageContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/assets/svgIcons/SvgIcon.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$CommonButton$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/Components/UI/CommonButton.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/Components/UI/NavLink.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$isEmpty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash/isEmpty.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/auth.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const Header = ()=>{
    const [loginToken, setLoginToken] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const [isFreeUser, setIsFreeUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isPricingPage, setIsPricingPage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const isHomePage = pathname === "/";
    // const props = usePage();
    const user = {};
    const { language, changeLanguage } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$context$2f$LanguageContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLanguage"])();
    // const user = usePage().props.auth.user;
    const signIn = !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$isEmpty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(loginToken);
    const [isActive, setIsActive] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isProductOpen, setIsProductOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLangOpen, setIsLangOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isOpenLanguage, setIsOpenLanguage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isDesktop, setIsDesktop] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    const toggleClass = ()=>setIsActive((prev)=>!prev);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleResize = ()=>{
            setIsDesktop(window.innerWidth >= 1200);
        };
        handleResize();
        window.addEventListener('resize', handleResize);
        return ()=>window.removeEventListener('resize', handleResize);
    }, []);
    const toggleProductDropdown = ()=>{
        setIsProductOpen((prev)=>!prev);
    };
    const handleProductMouseEnter = ()=>{
        if (isDesktop) {
            setIsProductOpen(true);
        }
    };
    const handleProductMouseLeave = ()=>{
        if (isDesktop) {
            setIsProductOpen(false);
        }
    };
    const toggleMobileLangDropdown = ()=>{
        setIsLangOpen((prev)=>!prev);
    };
    const toggleMobileLangEnter = ()=>{
        if (isDesktop) {
            setIsLangOpen(true);
        }
    };
    const toggleMobileLangLeave = ()=>{
        if (isDesktop) {
            setIsLangOpen(false);
        }
    };
    const toggleLanguageDropdown = ()=>{
        setIsOpenLanguage((prev)=>!prev);
    };
    const handleLanguageMouseEnter = ()=>setIsOpenLanguage(true);
    const handleLanguageMouseLeave = ()=>setIsOpenLanguage(false);
    const handleNavClick = ()=>{
        if (ref.current && document.body.clientWidth < 1220) {
            ref.current.click();
        }
    };
    const logoutUser = async ()=>{
        // First clear all local auth data
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].remove("authToken");
        sessionStorage.clear();
        localStorage.clear();
        // Then call the API to logout on server
        const success = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logout"])();
        // Always redirect to login page
        router.push("/login");
    };
    const renderUserDropdown = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(UserDropdown, {
            signIn: loginToken
        }, void 0, false, {
            fileName: "[project]/src/Components/UI/Header.js",
            lineNumber: 123,
            columnNumber: 36
        }, this);
    const lang = [
        {
            lang: "en",
            title: "English"
        },
        {
            lang: "fr",
            title: "French"
        },
        {
            lang: "es",
            title: "Español"
        }
    ];
    const changeLang = (lang)=>{
        changeLanguage(lang);
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const tokens = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get("authToken");
        setLoginToken(tokens);
        setIsPricingPage(pathname === "/pricing");
        try {
            const user = JSON.parse(localStorage.getItem("user"));
            if (user?.subscription_id === 1) {
                setIsFreeUser(true);
            }
        } catch (e) {
            console.warn("Invalid user in localStorage");
        }
        setLoading(false);
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isActive) {
            document.body.style.overflow = "hidden";
        } else {
            document.body.style.overflow = "auto";
        }
        // Cleanup on unmount
        return ()=>{
            document.body.style.overflow = "auto";
        };
    }, [
        isActive
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: `${isHomePage ? "home-page" : ""}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `siteHeader ${isActive ? "openmenu" : ""}`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Navbar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Navbar$3e$__["Navbar"], {
                expand: "xl",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Container$3e$__["Container"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "d-flex align-items-center ",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Navbar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Navbar$3e$__["Navbar"].Toggle, {
                                    ref: ref,
                                    onClick: toggleClass
                                }, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 181,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/",
                                    className: "brandLogo",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: "https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg",
                                        alt: "Brand Logo"
                                    }, void 0, false, {
                                        fileName: "[project]/src/Components/UI/Header.js",
                                        lineNumber: 184,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 182,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 180,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Navbar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Navbar$3e$__["Navbar"].Collapse, {
                            className: "justify-content-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "d-flex justify-content-center align-items-center openmenuSidebar",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            onClick: handleNavClick,
                                            href: "/",
                                            className: "brandLogo d-block d-xl-none",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                src: "https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg",
                                                alt: "Brand Logo"
                                            }, void 0, false, {
                                                fileName: "[project]/src/Components/UI/Header.js",
                                                lineNumber: 195,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/Components/UI/Header.js",
                                            lineNumber: 189,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Navbar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Navbar$3e$__["Navbar"].Toggle, {
                                            ref: ref,
                                            onClick: toggleClass
                                        }, void 0, false, {
                                            fileName: "[project]/src/Components/UI/Header.js",
                                            lineNumber: 197,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 188,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "navMenu d-xl-flex",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            onClick: handleNavClick,
                                            href: "/marketplace",
                                            className: "nav-link",
                                            children: "Marketplace"
                                        }, void 0, false, {
                                            fileName: "[project]/src/Components/UI/Header.js",
                                            lineNumber: 201,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `nav-item common_dropdown dropdown ${isProductOpen ? "show" : ""}`,
                                            onMouseEnter: handleProductMouseEnter,
                                            onMouseLeave: handleProductMouseLeave,
                                            onClick: toggleProductDropdown,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    className: "nav-link dropdown-toggle",
                                                    href: "#",
                                                    id: "navbarDropdown",
                                                    role: "button",
                                                    "aria-haspopup": "true",
                                                    "aria-expanded": isProductOpen ? "true" : "false",
                                                    children: "Products"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/Components/UI/Header.js",
                                                    lineNumber: 216,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: `dropdown-menu ${isProductOpen ? "show" : ""}`,
                                                    "aria-labelledby": "navbarDropdown",
                                                    children: [
                                                        !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$isEmpty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(loginToken) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            onClick: handleNavClick,
                                                            href: "/dashboard/trading-calculator",
                                                            className: "nav-link",
                                                            children: "Trading Calculators"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/Components/UI/Header.js",
                                                            lineNumber: 232,
                                                            columnNumber: 23
                                                        }, this),
                                                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$isEmpty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(loginToken) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            onClick: handleNavClick,
                                                            href: "trading-calculator",
                                                            className: "nav-link",
                                                            children: "Trading Calculators"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/Components/UI/Header.js",
                                                            lineNumber: 237,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            onClick: handleNavClick,
                                                            href: "/features",
                                                            className: "nav-link",
                                                            children: "Features"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/Components/UI/Header.js",
                                                            lineNumber: 241,
                                                            columnNumber: 21
                                                        }, this),
                                                        loginToken ? // <Link
                                                        //   onClick={handleNavClick}
                                                        //   href={
                                                        //     isFreeUser ? "/pricing?source=free_header_menu_pricing&feature=buy_trial"
                                                        //       : "/pricing?source=member_header_menu_pricing&feature=buy_trial"
                                                        //   }
                                                        //   className="nav-link"
                                                        // >
                                                        //   Pricing
                                                        // </Link>
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                            href: "#",
                                                            onClick: (e)=>{
                                                                e.preventDefault();
                                                                handleNavClick(); // keep this if you want the sidebar to close
                                                                const user = JSON.parse(localStorage.getItem("user"));
                                                                const isFree = user?.subscription_id === 1;
                                                                console.log("user?.subscription_iddddddddd", user?.subscription_id);
                                                                const targetUrl = isFree ? "/pricing?source=free_header_menu_pricing&feature=buy_trial" : "/pricing?source=member_header_menu_pricing&feature=buy_trial";
                                                                window.location.href = targetUrl;
                                                            },
                                                            className: "nav-link",
                                                            children: "Pricing"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/Components/UI/Header.js",
                                                            lineNumber: 256,
                                                            columnNumber: 23
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            onClick: handleNavClick,
                                                            href: "/pricing",
                                                            className: "nav-link",
                                                            children: "Pricing"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/Components/UI/Header.js",
                                                            lineNumber: 276,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/Components/UI/Header.js",
                                                    lineNumber: 227,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/Components/UI/Header.js",
                                            lineNumber: 210,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            onClick: handleNavClick,
                                            href: "/education",
                                            className: "nav-link",
                                            children: "Education"
                                        }, void 0, false, {
                                            fileName: "[project]/src/Components/UI/Header.js",
                                            lineNumber: 287,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            onClick: handleNavClick,
                                            href: "/blog",
                                            className: "nav-link",
                                            children: "Blog"
                                        }, void 0, false, {
                                            fileName: "[project]/src/Components/UI/Header.js",
                                            lineNumber: 294,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `nav-item common_dropdown dropdown d-block d-xl-none ${isLangOpen ? "show" : ""}`,
                                            onMouseEnter: toggleMobileLangEnter,
                                            onMouseLeave: toggleMobileLangLeave,
                                            onClick: toggleMobileLangDropdown,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    className: "nav-link dropdown-toggle",
                                                    href: "#",
                                                    id: "navbarDropdown",
                                                    role: "button",
                                                    "aria-haspopup": "true",
                                                    "aria-expanded": isLangOpen ? "true" : "false",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "globalIcon",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GlobalIcons"], {}, void 0, false, {
                                                                fileName: "[project]/src/Components/UI/Header.js",
                                                                lineNumber: 318,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/Components/UI/Header.js",
                                                            lineNumber: 317,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-capitalize fs-5 ms-2",
                                                            children: language
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/Components/UI/Header.js",
                                                            lineNumber: 320,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/Components/UI/Header.js",
                                                    lineNumber: 309,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: `dropdown-menu ${isLangOpen ? "show" : ""}`,
                                                    "aria-labelledby": "navbarDropdown",
                                                    children: lang.map((Language)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            onClick: ()=>changeLang(Language.lang),
                                                            href: "",
                                                            className: "nav-link text-white d-flex flex-column gap-3 fs-5 fw-bold",
                                                            children: Language.title
                                                        }, Language.lang, false, {
                                                            fileName: "[project]/src/Components/UI/Header.js",
                                                            lineNumber: 328,
                                                            columnNumber: 23
                                                        }, this))
                                                }, void 0, false, {
                                                    fileName: "[project]/src/Components/UI/Header.js",
                                                    lineNumber: 323,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/Components/UI/Header.js",
                                            lineNumber: 303,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "d-block d-xl-none",
                                            children: [
                                                signIn ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                        href: "",
                                                        className: "nav-link",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "me-3",
                                                                onClick: ()=>logoutUser(),
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SignoutIcon"], {}, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Header.js",
                                                                    lineNumber: 344,
                                                                    columnNumber: 110
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/Components/UI/Header.js",
                                                                lineNumber: 344,
                                                                columnNumber: 58
                                                            }, this),
                                                            " Sign Out"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/Components/UI/Header.js",
                                                        lineNumber: 344,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "/login",
                                                    className: "nav-link",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "me-3",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SignoutIcon"], {}, void 0, false, {
                                                                fileName: "[project]/src/Components/UI/Header.js",
                                                                lineNumber: 348,
                                                                columnNumber: 85
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/Components/UI/Header.js",
                                                            lineNumber: 348,
                                                            columnNumber: 62
                                                        }, this),
                                                        " Log In"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/Components/UI/Header.js",
                                                    lineNumber: 348,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "/help",
                                                    className: "nav-link",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "me-3",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HelpIcon"], {}, void 0, false, {
                                                                fileName: "[project]/src/Components/UI/Header.js",
                                                                lineNumber: 351,
                                                                columnNumber: 82
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/Components/UI/Header.js",
                                                            lineNumber: 351,
                                                            columnNumber: 59
                                                        }, this),
                                                        " Help Center"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/Components/UI/Header.js",
                                                    lineNumber: 351,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "/partner",
                                                    className: "nav-link white_stroke_icon",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "me-3",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PartnershipIcon"], {}, void 0, false, {
                                                                fileName: "[project]/src/Components/UI/Header.js",
                                                                lineNumber: 352,
                                                                columnNumber: 106
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/Components/UI/Header.js",
                                                            lineNumber: 352,
                                                            columnNumber: 83
                                                        }, this),
                                                        " Partnership"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/Components/UI/Header.js",
                                                    lineNumber: 352,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "/refer-a-friend",
                                                    className: "nav-link",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "me-3",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReferIcon"], {}, void 0, false, {
                                                                fileName: "[project]/src/Components/UI/Header.js",
                                                                lineNumber: 353,
                                                                columnNumber: 95
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/Components/UI/Header.js",
                                                            lineNumber: 353,
                                                            columnNumber: 72
                                                        }, this),
                                                        " Refer A Friend"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/Components/UI/Header.js",
                                                    lineNumber: 353,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/Components/UI/Header.js",
                                            lineNumber: 340,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 200,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 187,
                            columnNumber: 13
                        }, this),
                        isActive && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            onClick: handleNavClick,
                            className: "sidebar_backdrop d-xl-none"
                        }, void 0, false, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 359,
                            columnNumber: 15
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "languageDropdown d-none d-xl-flex",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `nav-item common_dropdown dropdown ${isOpenLanguage ? "show" : ""}`,
                                onMouseEnter: handleLanguageMouseEnter,
                                onMouseLeave: handleLanguageMouseLeave,
                                onClick: toggleLanguageDropdown,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        className: "nav-link dropdown-toggle",
                                        href: "#",
                                        id: "navbarDropdown",
                                        role: "button",
                                        "aria-haspopup": "true",
                                        "aria-expanded": isOpenLanguage ? "true" : "false",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `globalIcon ${isOpenLanguage ? "active" : ""}`,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-black-global.svg",
                                                        alt: "Global Icon Black",
                                                        className: "icon black"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/Components/UI/Header.js",
                                                        lineNumber: 381,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                        src: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-brand-blue-global.svg",
                                                        alt: "Global Icon Blue",
                                                        className: "icon blue"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/Components/UI/Header.js",
                                                        lineNumber: 386,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/Components/UI/Header.js",
                                                lineNumber: 380,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-capitalize fs-5 ms-2",
                                                children: language
                                            }, void 0, false, {
                                                fileName: "[project]/src/Components/UI/Header.js",
                                                lineNumber: 392,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/Components/UI/Header.js",
                                        lineNumber: 372,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `dropdown-menu ${isOpenLanguage ? "show" : ""}`,
                                        "aria-labelledby": "navbarDropdown",
                                        children: lang.map((Language)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                onClick: ()=>changeLang(Language.lang),
                                                href: "",
                                                className: "nav-link text-white d-flex flex-column gap-3 p-2 px-3 fs-5 fw-bold",
                                                children: Language.title
                                            }, Language.lang, false, {
                                                fileName: "[project]/src/Components/UI/Header.js",
                                                lineNumber: 400,
                                                columnNumber: 21
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/Components/UI/Header.js",
                                        lineNumber: 395,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/Components/UI/Header.js",
                                lineNumber: 366,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 365,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mx-2 mx-xl-4 d-none d-xl-block",
                            children: renderUserDropdown()
                        }, void 0, false, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 412,
                            columnNumber: 13
                        }, this),
                        !loading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                !loginToken && pathname !== "/pricing" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/pricing",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$CommonButton$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        className: "gradient-btn",
                                        title: "Get started"
                                    }, void 0, false, {
                                        fileName: "[project]/src/Components/UI/Header.js",
                                        lineNumber: 419,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 418,
                                    columnNumber: 19
                                }, this),
                                loginToken && isFreeUser && !isPricingPage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/pricing?source=free_header_upgrade_button&feature=buy_trial",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        className: "btn-style gradient-btn ",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "d-flex flex-column align-items-center text-center gap-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    style: {
                                                        lineHeight: "1"
                                                    },
                                                    children: "Upgrade Now"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/Components/UI/Header.js",
                                                    lineNumber: 427,
                                                    columnNumber: 25
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    style: {
                                                        fontSize: "0.70em",
                                                        lineHeight: "1"
                                                    },
                                                    children: "30-Day Free Trial"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/Components/UI/Header.js",
                                                    lineNumber: 428,
                                                    columnNumber: 25
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/Components/UI/Header.js",
                                            lineNumber: 426,
                                            columnNumber: 23
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/Components/UI/Header.js",
                                        lineNumber: 425,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 424,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, void 0, true)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/Components/UI/Header.js",
                    lineNumber: 179,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/Components/UI/Header.js",
                lineNumber: 178,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/Components/UI/Header.js",
            lineNumber: 177,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/Components/UI/Header.js",
        lineNumber: 176,
        columnNumber: 5
    }, this);
};
const UserDropdown = ({ signIn })=>{
    const [isUserDropdownOpen, setIsUserDropdownOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const router1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const logoutUser = async ()=>{
        // First clear all local auth data
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].remove("authToken");
        sessionStorage.clear();
        localStorage.clear();
        // Then call the API to logout on server
        const success = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logout"])();
        // Always redirect to login page
        router1.push("/login");
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Dropdown$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Dropdown$3e$__["Dropdown"], {
        align: "end",
        className: "common_dropdown userDropdown",
        show: isUserDropdownOpen,
        onToggle: (isOpen)=>setIsUserDropdownOpen(isOpen),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Dropdown$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Dropdown$3e$__["Dropdown"].Toggle, {
                variant: "",
                id: "dropdown-basic",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "user_icon",
                        children: isUserDropdownOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserBluekIcon"], {}, void 0, false, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 471,
                            columnNumber: 33
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserBlackIcon"], {}, void 0, false, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 471,
                            columnNumber: 53
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/Header.js",
                        lineNumber: 470,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "user_name"
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/Header.js",
                        lineNumber: 473,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/Components/UI/Header.js",
                lineNumber: 469,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Dropdown$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Dropdown$3e$__["Dropdown"].Menu, {
                children: signIn ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: "/dashboard",
                            className: "dropdown-item white_icon flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DashboardIcon"], {
                                    color: "image_color_to_white"
                                }, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 482,
                                    columnNumber: 15
                                }, this),
                                " Dashboard"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 478,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: "/help",
                            className: "dropdown-item white_icon flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HelpIcon"], {}, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 488,
                                    columnNumber: 15
                                }, this),
                                " Help Center"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 484,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: "/account/overview",
                            className: "dropdown-item white_icon flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SettingIcon"], {
                                    color: "image_color_to_white"
                                }, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 494,
                                    columnNumber: 15
                                }, this),
                                " Account Settings"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 490,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: "/partner",
                            className: "dropdown-item white_stroke_icon flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PartnershipIcon"], {}, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 500,
                                    columnNumber: 15
                                }, this),
                                " Partnership"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 496,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: "/refer-a-friend",
                            className: "dropdown-item white_icon flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReferIcon"], {}, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 506,
                                    columnNumber: 15
                                }, this),
                                " Refer A Friend"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 502,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: "/login",
                            onClick: ()=>{
                                logoutUser();
                            },
                            className: "dropdown-item white_icon flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SignoutIcon"], {}, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 513,
                                    columnNumber: 15
                                }, this),
                                " Log Out"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 508,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: "/login",
                            className: "dropdown-item d-flex align-items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SignoutIcon"], {}, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 519,
                                    columnNumber: 15
                                }, this),
                                " Log In"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 518,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: "/help",
                            className: "dropdown-item d-flex align-items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HelpIcon"], {}, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 522,
                                    columnNumber: 15
                                }, this),
                                " Help Center"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 521,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: "/partner",
                            className: "dropdown-item d-flex align-items-center white_stroke_icon",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PartnershipIcon"], {}, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 525,
                                    columnNumber: 15
                                }, this),
                                " Partnership"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 524,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$NavLink$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: "/refer-a-friend",
                            className: "dropdown-item d-flex align-items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$svgIcons$2f$SvgIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReferIcon"], {}, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Header.js",
                                    lineNumber: 528,
                                    columnNumber: 15
                                }, this),
                                " Refer A Friend"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/Components/UI/Header.js",
                            lineNumber: 527,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true)
            }, void 0, false, {
                fileName: "[project]/src/Components/UI/Header.js",
                lineNumber: 475,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/Components/UI/Header.js",
        lineNumber: 463,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Header;
}}),
"[project]/src/Components/UI/Footer.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Col$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Col.js [app-ssr] (ecmascript) <export default as Col>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Container$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Container.js [app-ssr] (ecmascript) <export default as Container>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Row$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Row.js [app-ssr] (ecmascript) <export default as Row>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
const Footer = ()=>{
    const url = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const [loginToken, setLoginToken] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isFreeUser, setIsFreeUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const tokens = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get("authToken");
        setLoginToken(tokens);
        try {
            const user = JSON.parse(localStorage.getItem("user"));
            if (user?.subscription_id === 1) {
                setIsFreeUser(true);
            }
        } catch (e) {
            console.warn("Invalid user in localStorage");
        }
    }, []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "site_footer",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "site_footer_inner",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Container$3e$__["Container"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Row$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__["Row"], {
                            className: "gx-xl-5",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Col$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                                    md: 4,
                                    sm: 12,
                                    xs: 12,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "site_footer_content",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "site_footer_logo",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    prefetch: true,
                                                    href: "/",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                        src: "https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg",
                                                        alt: "Brand Logo"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                        lineNumber: 43,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                    lineNumber: 42,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/Components/UI/Footer.js",
                                                lineNumber: 41,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "TradeReply is an advanced analytics suite designed for crypto and stock traders to input historical trading data and leverage powerful visuals, graphs, and metrics to optimize and develop effective trade strategies with real-time insights."
                                            }, void 0, false, {
                                                fileName: "[project]/src/Components/UI/Footer.js",
                                                lineNumber: 46,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/Components/UI/Footer.js",
                                        lineNumber: 40,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Footer.js",
                                    lineNumber: 39,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Col$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                                    md: 8,
                                    sm: 12,
                                    xs: 12,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Row$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__["Row"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Col$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                                                md: 4,
                                                sm: 4,
                                                xs: 6,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "site_footer_links",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                            children: "Company"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/Components/UI/Footer.js",
                                                            lineNumber: 59,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/help/hc/en-us/requests/new",
                                                                        className: url == '/help/hc/en-us/requests/new' ? 'new-link' : '',
                                                                        children: "Contact"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 62,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 61,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/brand-assets",
                                                                        className: url == '/brand-assets' ? 'new-link' : '',
                                                                        children: "Brand Assets"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 65,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 64,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/accessibility",
                                                                        className: url == '/accessibility' ? 'new-link' : '',
                                                                        children: "Accessibility"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 68,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 67,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/privacy",
                                                                        className: url == '/privacy' ? 'new-link' : '',
                                                                        children: "Privacy Policy"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 71,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 70,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/cookies",
                                                                        className: url == '/cookies' ? 'new-link' : '',
                                                                        children: "Cookies Policy"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 76,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 75,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                                        href: "#",
                                                                        onClick: (e)=>{
                                                                            e.preventDefault(); // prevents scroll to top
                                                                            if (typeof Osano !== "undefined" && Osano.cm) {
                                                                                Osano.cm.showDrawer("osano-cm-dom-info-dialog-open");
                                                                            }
                                                                        },
                                                                        children: "Cookie Settings"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 81,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 80,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/terms",
                                                                        className: url == '/terms' ? 'new-link' : '',
                                                                        children: "Terms & Conditions"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 94,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 93,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/disclaimer",
                                                                        className: url == '/disclaimer' ? 'new-link' : '',
                                                                        children: "Disclaimer"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 99,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 98,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/Components/UI/Footer.js",
                                                            lineNumber: 60,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                    lineNumber: 58,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/Components/UI/Footer.js",
                                                lineNumber: 57,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Col$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                                                md: 4,
                                                sm: 4,
                                                xs: 6,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "site_footer_links",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                            children: "Partners"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/Components/UI/Footer.js",
                                                            lineNumber: 109,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/refer-a-friend",
                                                                        className: url == '/refer-a-friend' ? 'new-link' : '',
                                                                        children: "Refer a Friend"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 112,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 111,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/partner",
                                                                        className: url == '/partner' ? 'new-link' : '',
                                                                        children: "Partner Program"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 115,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 114,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/advertising",
                                                                        className: url == '/advertising' ? 'new-link' : '',
                                                                        children: "Advertising"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 118,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 117,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/features",
                                                                        className: url == '/features' ? 'new-link' : '',
                                                                        children: "Features"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 121,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 120,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/education",
                                                                        className: url == '/education' ? 'new-link' : '',
                                                                        children: "Education"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 124,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 123,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/brokers",
                                                                        className: url == '/brokers' ? 'new-link' : '',
                                                                        children: "Brokers"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 127,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 126,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/Components/UI/Footer.js",
                                                            lineNumber: 110,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                    lineNumber: 108,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/Components/UI/Footer.js",
                                                lineNumber: 107,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Col$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                                                md: 4,
                                                sm: 4,
                                                xs: 6,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "site_footer_links",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                            children: "Community"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/Components/UI/Footer.js",
                                                            lineNumber: 134,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/help",
                                                                        className: url == '/helpcenter' ? 'new-link' : '',
                                                                        children: "Help Center"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 137,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 136,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/sitemap",
                                                                        className: url == '/sitemap' ? 'new-link' : '',
                                                                        children: "Sitemap"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 140,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 139,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: loginToken ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: loginToken ? isFreeUser ? "/pricing?source=free_footer_menu_pricing&feature=buy_trial" : "/pricing?source=member_footer_menu_pricing&feature=buy_trial" : "/pricing",
                                                                        className: url == "/pricing" ? "new-link" : "",
                                                                        children: "Pricing"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 151,
                                                                        columnNumber: 28
                                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/pricing",
                                                                        className: url == '/pricing' ? 'new-link' : '',
                                                                        children: "Pricing"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 166,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 142,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/blog",
                                                                        className: url == '/blog' ? 'new-link' : '',
                                                                        children: "Blog"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 174,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 173,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/status",
                                                                        className: url == '/status' ? 'new-link' : '',
                                                                        children: "Status"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 177,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 176,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        prefetch: true,
                                                                        href: "/help//hc/en-us/requests/new?ticket_form_id=37293785519643",
                                                                        className: url == '/help//hc/en-us/requests/new?ticket_form_id=37293785519643' ? 'new-link' : '',
                                                                        children: "Feedback/Bugs"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/Components/UI/Footer.js",
                                                                        lineNumber: 180,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                                    lineNumber: 179,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/Components/UI/Footer.js",
                                                            lineNumber: 135,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/Components/UI/Footer.js",
                                                    lineNumber: 133,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/Components/UI/Footer.js",
                                                lineNumber: 132,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/Components/UI/Footer.js",
                                        lineNumber: 56,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/Components/UI/Footer.js",
                                    lineNumber: 55,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/Components/UI/Footer.js",
                            lineNumber: 38,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/Footer.js",
                        lineNumber: 37,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/Components/UI/Footer.js",
                    lineNumber: 36,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "site_footer_copyright",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Container$3e$__["Container"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            children: "Copyright © 2025 TradeReply. All Rights Reserved."
                        }, void 0, false, {
                            fileName: "[project]/src/Components/UI/Footer.js",
                            lineNumber: 192,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/Components/UI/Footer.js",
                        lineNumber: 191,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/Components/UI/Footer.js",
                    lineNumber: 190,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/Components/UI/Footer.js",
            lineNumber: 35,
            columnNumber: 7
        }, this)
    }, void 0, false);
};
const __TURBOPACK__default__export__ = Footer;
}}),
"[project]/src/Seo/Schema/JsonLdSchema.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
/**
 * JsonLdSchema Component
 * 
 * Renders JSON-LD structured data schemas for SEO purposes.
 * Each schema is rendered in its own separate <script type="application/ld+json"> tag
 * to ensure proper search engine crawling and indexing.
 * 
 * Usage:
 * <JsonLdSchema schemas={[organizationSchema, websiteSchema]} />
 */ __turbopack_context__.s({
    "cleanKeywords": (()=>cleanKeywords),
    "default": (()=>JsonLdSchema),
    "formatDateToISO": (()=>formatDateToISO),
    "generateBlogPostingSchema": (()=>generateBlogPostingSchema),
    "generateBreadcrumbListSchema": (()=>generateBreadcrumbListSchema),
    "generateCategoryBreadcrumbs": (()=>generateCategoryBreadcrumbs),
    "generateCollectionPageSchema": (()=>generateCollectionPageSchema),
    "generateFallbackArticleBody": (()=>generateFallbackArticleBody),
    "generateFallbackArticles": (()=>generateFallbackArticles),
    "generateFallbackKeywords": (()=>generateFallbackKeywords),
    "generateFallbackProductData": (()=>generateFallbackProductData),
    "generateFallbackReviews": (()=>generateFallbackReviews),
    "generateOrganizationSchema": (()=>generateOrganizationSchema),
    "generateProductSchema": (()=>generateProductSchema),
    "generateWebsiteSchema": (()=>generateWebsiteSchema),
    "getBlogSlug": (()=>getBlogSlug),
    "selectReviewsForSchema": (()=>selectReviewsForSchema),
    "shuffleArray": (()=>shuffleArray)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function JsonLdSchema({ schemas = [] }) {
    if (!schemas || schemas.length === 0) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: schemas.map((schema, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
                type: "application/ld+json",
                dangerouslySetInnerHTML: {
                    __html: JSON.stringify(schema, null, 0)
                }
            }, index, false, {
                fileName: "[project]/src/Seo/Schema/JsonLdSchema.js",
                lineNumber: 20,
                columnNumber: 9
            }, this))
    }, void 0, false);
}
const generateOrganizationSchema = ()=>{
    return {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "TradeReply",
        "url": "https://www.tradereply.com",
        "logo": "https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png",
        "contactPoint": {
            "@type": "ContactPoint",
            "url": "https://www.tradereply.com/help",
            "contactType": "Customer Support",
            "areaServed": "Global",
            "availableLanguage": "English"
        },
        "sameAs": [
            "https://www.facebook.com/TradeReply",
            "https://www.instagram.com/tradereply",
            "https://x.com/JoinTradeReply"
        ]
    };
};
const generateWebsiteSchema = ()=>{
    return {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "url": "https://www.tradereply.com/",
        "name": "TradeReply"
    };
};
const generateBlogPostingSchema = ({ canonicalUrl, headline, description, imageUrl, datePublished, dateModified, articleBody, keywords, blogData = null })=>{
    // Only generate schema if required fields are present
    if (!canonicalUrl || !headline) {
        return null;
    }
    // Generate fallback content if blogData is provided
    let finalArticleBody = articleBody;
    let finalKeywords = keywords;
    if (blogData) {
        // Use fallback generation if articleBody is missing or too short
        if (!finalArticleBody || finalArticleBody.trim().length < 500) {
            finalArticleBody = generateFallbackArticleBody(blogData);
        }
        // Use fallback generation if keywords are missing or insufficient
        if (!finalKeywords || finalKeywords.trim().length === 0) {
            finalKeywords = generateFallbackKeywords(blogData);
        }
    }
    return {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": canonicalUrl
        },
        "headline": headline,
        "description": description || "",
        "image": imageUrl || "",
        "author": {
            "@type": "Organization",
            "name": "TradeReply"
        },
        "publisher": {
            "@type": "Organization",
            "name": "TradeReply",
            "logo": {
                "@type": "ImageObject",
                "url": "https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png"
            }
        },
        "datePublished": datePublished || "",
        "dateModified": dateModified || datePublished || "",
        "articleBody": finalArticleBody || description || "",
        "keywords": finalKeywords || ""
    };
};
const formatDateToISO = (date)=>{
    if (!date) return null;
    try {
        // Handle different date formats
        let dateObj;
        if (typeof date === 'string') {
            dateObj = new Date(date);
        } else if (date instanceof Date) {
            dateObj = date;
        } else {
            return null;
        }
        // Check if date is valid
        if (isNaN(dateObj.getTime())) {
            return null;
        }
        return dateObj.toISOString();
    } catch (error) {
        console.warn('Error formatting date to ISO:', error);
        return null;
    }
};
const getBlogSlug = (blog)=>{
    if (!blog) return '';
    // If slug exists, use it directly
    if (blog.slug) {
        return blog.slug;
    }
    // Fallback: generate slug from title
    if (blog.title) {
        return blog.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
    }
    return '';
};
const cleanKeywords = (keywords)=>{
    if (!keywords || typeof keywords !== 'string') {
        return '';
    }
    return keywords.split(',').map((keyword)=>keyword.trim()).filter((keyword)=>keyword.length > 0).join(', ');
};
const generateProductSchema = ({ name, description, image, brand, price, currency = "USD", availability = "http://schema.org/InStock", url, seller, aggregateRating, reviews = [], productData = null })=>{
    // Only generate schema if required fields are present
    if (!name || !price) {
        return null;
    }
    // Apply fallback data if productData is provided
    let enhancedData = {
        name,
        description,
        image,
        brand,
        price,
        currency,
        availability,
        url,
        seller,
        aggregateRating,
        reviews
    };
    if (productData) {
        enhancedData = generateFallbackProductData({
            ...enhancedData,
            ...productData
        });
    }
    const schema = {
        "@context": "https://schema.org",
        "@type": "Product",
        "name": enhancedData.name,
        "description": enhancedData.description || "",
        "image": enhancedData.image || "",
        "offers": {
            "@type": "Offer",
            "price": enhancedData.price.toString(),
            "priceCurrency": enhancedData.currency,
            "availability": enhancedData.availability,
            "url": enhancedData.url || ""
        }
    };
    // Add brand (always include with fallback)
    schema.brand = {
        "@type": "Brand",
        "name": enhancedData.brand
    };
    // Add seller (always include with fallback)
    schema.offers.seller = {
        "@type": "Organization",
        "name": enhancedData.seller?.name || enhancedData.brand,
        "url": enhancedData.seller?.url || "https://www.tradereply.com/marketplace"
    };
    // Add aggregate rating (always include with fallback)
    schema.aggregateRating = {
        "@type": "AggregateRating",
        "ratingValue": enhancedData.aggregateRating?.ratingValue?.toString() || "4.5",
        "reviewCount": enhancedData.aggregateRating?.reviewCount?.toString() || "25"
    };
    // Add reviews (use provided reviews or generate fallbacks)
    let finalReviews = enhancedData.reviews;
    if (!finalReviews || finalReviews.length === 0) {
        finalReviews = generateFallbackReviews(enhancedData, 3);
    }
    if (finalReviews && finalReviews.length > 0) {
        schema.review = finalReviews.slice(0, 3).map((review)=>({
                "@type": "Review",
                "author": {
                    "@type": "Person",
                    "name": review.author || "Anonymous"
                },
                "datePublished": formatDateToISO(review.datePublished) || "",
                "reviewBody": review.reviewBody || "",
                "reviewRating": {
                    "@type": "Rating",
                    "ratingValue": review.rating ? review.rating.toString() : "5"
                }
            }));
    }
    return schema;
};
const generateCollectionPageSchema = ({ name, description, url, articles = [], currentPage = 1 })=>{
    // Only generate schema if required fields are present
    if (!name || !url) {
        return null;
    }
    const pageTitle = currentPage > 1 ? `${name} – Page ${currentPage}` : name;
    // Fallback description if not provided
    const finalDescription = description || `Explore curated trading content and educational resources on TradeReply.com. ${pageTitle} contains valuable insights for traders of all levels.`;
    const schema = {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": pageTitle,
        "description": finalDescription,
        "url": url
    };
    // Process articles with fallback data
    let processedArticles = articles;
    // If no articles provided, create fallback articles
    if (!processedArticles || processedArticles.length === 0) {
        processedArticles = generateFallbackArticles(currentPage);
    }
    // Add articles as ListItem elements (maximum 10)
    if (processedArticles && processedArticles.length > 0) {
        schema.mainEntity = {
            "@type": "ItemList",
            "numberOfItems": processedArticles.length,
            "itemListElement": processedArticles.slice(0, 10).map((article, index)=>({
                    "@type": "ListItem",
                    "position": index + 1,
                    "item": {
                        "@type": article.type === 'blog' || article.type === 'education' ? article.type === 'blog' ? "BlogPosting" : "Article" : "BlogPosting",
                        "@id": `https://www.tradereply.com/${article.type || 'blog'}/${article.slug || 'article'}`,
                        "name": article.title || `Trading Article ${index + 1}`,
                        "description": article.summary || generateFallbackArticleBody({
                            title: article.title,
                            type: article.type
                        }).substring(0, 200) + '...',
                        "datePublished": formatDateToISO(article.created_at) || formatDateToISO(new Date()),
                        "author": {
                            "@type": "Organization",
                            "name": "TradeReply"
                        }
                    }
                }))
        };
    }
    return schema;
};
const generateBreadcrumbListSchema = ({ items = [] })=>{
    // Only generate schema if items are provided
    if (!items || items.length === 0) {
        return null;
    }
    return {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": items.map((item, index)=>({
                "@type": "ListItem",
                "position": index + 1,
                "name": item.name,
                "item": item.url
            }))
    };
};
const generateFallbackArticleBody = (article)=>{
    if (!article) return '';
    // Priority order: schema_article_body -> summary -> truncated content -> title
    if (article.schema_article_body && article.schema_article_body.trim().length >= 500) {
        return article.schema_article_body.trim();
    }
    if (article.summary && article.summary.trim().length > 0) {
        const summary = article.summary.trim();
        // If summary is already 500-600 chars, use it
        if (summary.length >= 500 && summary.length <= 600) {
            return summary;
        }
        // If summary is too short, expand it
        if (summary.length < 500) {
            const expansion = ` This comprehensive guide covers essential trading concepts, market analysis techniques, and strategic approaches to help traders improve their performance. Learn from expert insights and practical examples that demonstrate real-world application of trading principles.`;
            const expandedContent = summary + expansion;
            return expandedContent.length <= 600 ? expandedContent : expandedContent.substring(0, 597) + '...';
        }
        // If summary is too long, truncate it
        return summary.substring(0, 597) + '...';
    }
    // Fallback to truncated content if available
    if (article.content && article.content.trim().length > 0) {
        const cleanContent = article.content.replace(/<[^>]*>/g, '').trim(); // Remove HTML tags
        if (cleanContent.length >= 500) {
            return cleanContent.substring(0, 597) + '...';
        }
    }
    // Final fallback: generate from title
    if (article.title) {
        const baseContent = `${article.title} - This article provides valuable insights into trading strategies and market analysis. Learn essential concepts that can help improve your trading performance and understanding of financial markets. Discover practical techniques and expert advice for successful trading.`;
        if (baseContent.length >= 500) {
            return baseContent.length <= 600 ? baseContent : baseContent.substring(0, 597) + '...';
        }
        // Expand if still too short
        const expandedContent = baseContent + ` Explore comprehensive coverage of market fundamentals, risk management strategies, and advanced trading methodologies designed for both beginners and experienced traders.`;
        return expandedContent.length <= 600 ? expandedContent : expandedContent.substring(0, 597) + '...';
    }
    // Ultimate fallback
    return 'Comprehensive trading guide covering market analysis, strategic approaches, and practical techniques for successful trading. Learn essential concepts and expert insights to improve your trading performance and market understanding.';
};
const generateFallbackKeywords = (article)=>{
    if (!article) return 'trading, finance, investment, strategy, market analysis';
    // Use existing schema_keywords if available and valid
    if (article.schema_keywords && article.schema_keywords.trim().length > 0) {
        const keywords = article.schema_keywords.split(',').map((k)=>k.trim()).filter((k)=>k.length > 0);
        if (keywords.length >= 5 && keywords.length <= 8) {
            return article.schema_keywords.trim();
        }
    }
    // Generate keywords based on article type and content
    const baseKeywords = article.type === 'education' ? [
        'trading education',
        'financial learning',
        'market fundamentals',
        'investment basics',
        'trading course'
    ] : [
        'trading',
        'finance',
        'investment',
        'market analysis',
        'trading strategy'
    ];
    // Try to extract keywords from title
    const titleKeywords = [];
    if (article.title) {
        const title = article.title.toLowerCase();
        const tradingTerms = [
            'stock',
            'forex',
            'crypto',
            'options',
            'futures',
            'etf',
            'bond',
            'commodity',
            'dividend',
            'portfolio'
        ];
        const strategyTerms = [
            'strategy',
            'analysis',
            'technique',
            'method',
            'approach',
            'system',
            'indicator',
            'signal'
        ];
        tradingTerms.forEach((term)=>{
            if (title.includes(term)) titleKeywords.push(term);
        });
        strategyTerms.forEach((term)=>{
            if (title.includes(term)) titleKeywords.push(term);
        });
    }
    // Combine base keywords with extracted keywords
    const allKeywords = [
        ...baseKeywords,
        ...titleKeywords
    ];
    const uniqueKeywords = [
        ...new Set(allKeywords)
    ];
    // Ensure we have 5-8 keywords
    if (uniqueKeywords.length >= 8) {
        return uniqueKeywords.slice(0, 8).join(', ');
    } else if (uniqueKeywords.length >= 5) {
        return uniqueKeywords.join(', ');
    } else {
        // Add generic trading keywords to reach minimum of 5
        const additionalKeywords = [
            'financial markets',
            'risk management',
            'profit optimization'
        ];
        const finalKeywords = [
            ...uniqueKeywords,
            ...additionalKeywords
        ].slice(0, 8);
        return finalKeywords.join(', ');
    }
};
const generateFallbackProductData = (product)=>{
    if (!product) return null;
    const fallbackData = {
        ...product
    };
    // Fallback description
    if (!fallbackData.description || fallbackData.description.trim().length === 0) {
        fallbackData.description = fallbackData.name ? `${fallbackData.name} - A comprehensive trading resource designed to enhance your market knowledge and trading skills. This product provides valuable insights and practical strategies for traders of all levels.` : 'Professional trading resource with expert insights and practical strategies for market success.';
    }
    // Fallback brand (seller name)
    if (!fallbackData.brand && fallbackData.seller?.name) {
        fallbackData.brand = fallbackData.seller.name;
    } else if (!fallbackData.brand) {
        fallbackData.brand = 'TradeReply Marketplace';
    }
    // Fallback seller information
    if (!fallbackData.seller || !fallbackData.seller.name) {
        fallbackData.seller = {
            name: fallbackData.brand || 'TradeReply Seller',
            url: 'https://www.tradereply.com/marketplace'
        };
    }
    // Fallback aggregate rating
    if (!fallbackData.aggregateRating || !fallbackData.aggregateRating.ratingValue) {
        fallbackData.aggregateRating = {
            ratingValue: 4.5,
            reviewCount: 25
        };
    }
    // Fallback availability
    if (!fallbackData.availability) {
        fallbackData.availability = 'http://schema.org/InStock';
    }
    // Fallback currency
    if (!fallbackData.currency) {
        fallbackData.currency = 'USD';
    }
    return fallbackData;
};
const generateFallbackReviews = (product, count = 3)=>{
    if (!product) return [];
    const fallbackReviews = [
        {
            author: 'Sarah Johnson',
            datePublished: '2025-01-15T10:00:00Z',
            reviewBody: 'Excellent resource with practical insights. The content is well-structured and easy to follow. Highly recommended for traders looking to improve their skills.',
            rating: 5
        },
        {
            author: 'Michael Chen',
            datePublished: '2025-01-10T14:30:00Z',
            reviewBody: 'Great value for money. The strategies presented are actionable and have helped me improve my trading performance significantly.',
            rating: 4
        },
        {
            author: 'Emily Rodriguez',
            datePublished: '2025-01-05T09:15:00Z',
            reviewBody: 'Comprehensive and informative. Perfect for both beginners and experienced traders. The examples are clear and relevant.',
            rating: 5
        },
        {
            author: 'David Thompson',
            datePublished: '2024-12-28T16:45:00Z',
            reviewBody: 'Solid content with good practical applications. The author clearly knows the subject matter well.',
            rating: 4
        }
    ];
    return fallbackReviews.slice(0, count);
};
const generateFallbackArticles = (currentPage = 1, count = 10)=>{
    const baseArticles = [
        {
            title: 'Advanced Trading Strategies for Market Success',
            slug: 'advanced-trading-strategies-market-success',
            summary: 'Learn proven trading strategies that professional traders use to maximize profits and minimize risks in volatile markets.',
            type: 'blog',
            created_at: '2025-01-20T10:00:00Z'
        },
        {
            title: 'Understanding Market Analysis and Technical Indicators',
            slug: 'understanding-market-analysis-technical-indicators',
            summary: 'Comprehensive guide to technical analysis, chart patterns, and key indicators for making informed trading decisions.',
            type: 'education',
            created_at: '2025-01-18T14:30:00Z'
        },
        {
            title: 'Risk Management Fundamentals for Traders',
            slug: 'risk-management-fundamentals-traders',
            summary: 'Essential risk management techniques to protect your capital and ensure long-term trading success.',
            type: 'blog',
            created_at: '2025-01-15T09:15:00Z'
        },
        {
            title: 'Cryptocurrency Trading: A Beginner\'s Guide',
            slug: 'cryptocurrency-trading-beginners-guide',
            summary: 'Complete introduction to cryptocurrency trading, including market basics, popular coins, and trading strategies.',
            type: 'education',
            created_at: '2025-01-12T16:45:00Z'
        },
        {
            title: 'Options Trading Strategies for Income Generation',
            slug: 'options-trading-strategies-income-generation',
            summary: 'Explore various options trading strategies designed to generate consistent income in different market conditions.',
            type: 'blog',
            created_at: '2025-01-10T11:20:00Z'
        },
        {
            title: 'Forex Market Fundamentals and Currency Pairs',
            slug: 'forex-market-fundamentals-currency-pairs',
            summary: 'Understanding the forex market, major currency pairs, and factors that influence exchange rates.',
            type: 'education',
            created_at: '2025-01-08T13:00:00Z'
        },
        {
            title: 'Building a Diversified Investment Portfolio',
            slug: 'building-diversified-investment-portfolio',
            summary: 'Learn how to create a well-balanced portfolio that spreads risk across different asset classes and sectors.',
            type: 'blog',
            created_at: '2025-01-05T08:30:00Z'
        },
        {
            title: 'Market Psychology and Emotional Trading',
            slug: 'market-psychology-emotional-trading',
            summary: 'Understanding the psychological aspects of trading and how emotions can impact trading decisions.',
            type: 'education',
            created_at: '2025-01-03T15:45:00Z'
        },
        {
            title: 'Day Trading vs Swing Trading: Which is Right for You?',
            slug: 'day-trading-vs-swing-trading-comparison',
            summary: 'Compare different trading styles to determine which approach aligns with your goals and lifestyle.',
            type: 'blog',
            created_at: '2025-01-01T12:00:00Z'
        },
        {
            title: 'Economic Indicators and Their Impact on Markets',
            slug: 'economic-indicators-impact-markets',
            summary: 'Learn how key economic indicators affect market movements and how to use them in your trading strategy.',
            type: 'education',
            created_at: '2024-12-30T10:15:00Z'
        }
    ];
    // Adjust articles based on page number to simulate pagination
    const startIndex = (currentPage - 1) * count;
    const selectedArticles = [];
    for(let i = 0; i < count; i++){
        const articleIndex = (startIndex + i) % baseArticles.length;
        const baseArticle = baseArticles[articleIndex];
        // Modify title slightly for different pages to simulate unique content
        const pageModifier = currentPage > 1 ? ` - Page ${currentPage} Insights` : '';
        selectedArticles.push({
            ...baseArticle,
            title: baseArticle.title + pageModifier,
            slug: baseArticle.slug + (currentPage > 1 ? `-page-${currentPage}` : '')
        });
    }
    return selectedArticles;
};
const selectReviewsForSchema = (allReviews = [], averageRating = 5, maxReviews = 3)=>{
    if (!allReviews || allReviews.length === 0) {
        return [];
    }
    // Round average rating to nearest integer for selection logic
    const targetRating = Math.round(averageRating);
    // Filter reviews by target rating
    const targetReviews = allReviews.filter((review)=>Math.round(parseFloat(review.rating || 5)) === targetRating);
    // If we have enough reviews of the target rating, use them
    if (targetReviews.length >= maxReviews) {
        return shuffleArray(targetReviews).slice(0, maxReviews);
    }
    // If not enough target reviews, include nearby ratings
    const nearbyRatings = [
        targetRating,
        targetRating - 1,
        targetRating + 1
    ].filter((r)=>r >= 1 && r <= 5);
    const nearbyReviews = allReviews.filter((review)=>nearbyRatings.includes(Math.round(parseFloat(review.rating || 5))));
    return shuffleArray(nearbyReviews).slice(0, maxReviews);
};
const shuffleArray = (array)=>{
    const shuffled = [
        ...array
    ];
    for(let i = shuffled.length - 1; i > 0; i--){
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [
            shuffled[j],
            shuffled[i]
        ];
    }
    return shuffled;
};
const generateCategoryBreadcrumbs = (categoryName = "All Articles", currentPage = null)=>{
    const breadcrumbs = [
        {
            name: "Home",
            url: "https://www.tradereply.com/"
        },
        {
            name: categoryName,
            url: "https://www.tradereply.com/category"
        }
    ];
    // Add page breadcrumb for paginated pages
    if (currentPage && currentPage > 1) {
        breadcrumbs.push({
            name: `Page ${currentPage}`,
            url: `https://www.tradereply.com/category/page/${currentPage}`
        });
    }
    return breadcrumbs;
};
}}),
"[project]/src/constants/defaultMetadata.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
/**
 * Default Metadata Constants
 * 
 * This file contains the default site-wide metadata and schemas
 * that are used as fallbacks when pages don't provide their own.
 */ __turbopack_context__.s({
    "DEFAULT_META_PROPS": (()=>DEFAULT_META_PROPS),
    "DEFAULT_SCHEMAS": (()=>DEFAULT_SCHEMAS),
    "mergeMetaProps": (()=>mergeMetaProps),
    "mergeSchemas": (()=>mergeSchemas)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Seo$2f$Schema$2f$JsonLdSchema$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/Seo/Schema/JsonLdSchema.js [app-ssr] (ecmascript)");
;
const DEFAULT_META_PROPS = {
    title: "TradeReply: Advanced Trading Tools & Strategy Optimization",
    description: "Optimize your trades with TradeReply.com. Access powerful trading strategies, real‑time analytics, and tools for crypto and stock market success.",
    canonical_link: "https://www.tradereply.com/",
    og_title: "TradeReply: Advanced Trading Tools & Strategy Optimization",
    og_description: "Optimize your trades with TradeReply.com. Access powerful trading strategies, real‑time analytics, and tools for crypto and stock market success.",
    og_site_name: "TradeReply",
    og_image: "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
    og_type: "website",
    twitter_title: "TradeReply: Advanced Trading Tools & Strategy Optimization",
    twitter_description: "Optimize your trades with TradeReply.com. Access powerful trading strategies, real‑time analytics, and tools for crypto and stock market success.",
    twitter_image: "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
    twitter_card: "summary_large_image",
    twitter_site: "@JoinTradeReply"
};
const DEFAULT_SCHEMAS = [
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Seo$2f$Schema$2f$JsonLdSchema$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generateOrganizationSchema"])(),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Seo$2f$Schema$2f$JsonLdSchema$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generateWebsiteSchema"])()
];
const mergeMetaProps = (pageMetaProps = {})=>{
    return {
        ...DEFAULT_META_PROPS,
        ...pageMetaProps
    };
};
const mergeSchemas = (pageSchemas = [])=>{
    return [
        ...DEFAULT_SCHEMAS,
        ...pageSchemas
    ];
};
}}),
"[project]/src/context/MetaContext.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MetaContextProvider": (()=>MetaContextProvider),
    "useMetaContext": (()=>useMetaContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$defaultMetadata$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/defaultMetadata.js [app-ssr] (ecmascript)");
"use client";
;
;
;
const MetaContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])();
const useMetaContext = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(MetaContext);
    if (!context) {
        throw new Error('useMetaContext must be used within a MetaContextProvider');
    }
    return context;
};
const MetaContextProvider = ({ children })=>{
    const [currentMetaProps, setCurrentMetaProps] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$defaultMetadata$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_META_PROPS"]);
    const [currentSchemas, setCurrentSchemas] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$defaultMetadata$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_SCHEMAS"]);
    /**
   * Update metadata for the current page
   * @param {Object} metaProps - Page-specific metadata
   * @param {Array} schemas - Page-specific schemas
   */ const updateMeta = (metaProps = {}, schemas = [])=>{
        setCurrentMetaProps((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$defaultMetadata$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeMetaProps"])(metaProps));
        setCurrentSchemas((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$defaultMetadata$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeSchemas"])(schemas));
    };
    /**
   * Reset to default metadata (useful for navigation)
   */ const resetToDefaults = ()=>{
        setCurrentMetaProps(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$defaultMetadata$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_META_PROPS"]);
        setCurrentSchemas(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$defaultMetadata$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_SCHEMAS"]);
    };
    const value = {
        currentMetaProps,
        currentSchemas,
        updateMeta,
        resetToDefaults
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(MetaContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/context/MetaContext.js",
        lineNumber: 46,
        columnNumber: 5
    }, this);
};
}}),
"[project]/src/Layouts/HomeLayout.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$Header$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/Components/UI/Header.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$Footer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/Components/UI/Footer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$context$2f$LanguageContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/context/LanguageContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$context$2f$MetaContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/context/MetaContext.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
/**
 * HomeLayout Component
 *
 * @param {Object} props
 * @param {React.ReactNode} props.children - Page content
 * @param {Object} props.metaProps - Page-specific metadata (optional)
 * @param {Array} props.schemas - Page-specific JSON-LD schemas (optional)
 */ const HomeLayout = ({ children, metaProps = {}, schemas = [] })=>{
    const { updateMeta, resetToDefaults } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$context$2f$MetaContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMetaContext"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Update metadata when component mounts or props change
        if (Object.keys(metaProps).length > 0 || schemas.length > 0) {
            updateMeta(metaProps, schemas);
        } else {
            // Reset to defaults if no custom meta provided
            resetToDefaults();
        }
        // Cleanup: reset to defaults when component unmounts
        return ()=>{
            resetToDefaults();
        };
    }, [
        metaProps,
        schemas,
        updateMeta,
        resetToDefaults
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$context$2f$LanguageContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LanguageProvider"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$Header$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/Layouts/HomeLayout.js",
                    lineNumber: 44,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                    className: "main-content",
                    children: children
                }, void 0, false, {
                    fileName: "[project]/src/Layouts/HomeLayout.js",
                    lineNumber: 46,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$Footer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/Layouts/HomeLayout.js",
                    lineNumber: 47,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/Layouts/HomeLayout.js",
            lineNumber: 43,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/Layouts/HomeLayout.js",
        lineNumber: 42,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = HomeLayout;
}}),

};

//# sourceMappingURL=src_cc93c87a._.js.map