/* [project]/src/css/Home/Switch.scss.css [app-client] (css) */
.form-check-input {
  position: relative;
  cursor: pointer;
  appearance: none;
  transition: background-color .3s;
  width: 72px !important;
  height: 34px !important;
  border-radius: 30px !important;
  background-color: #fff !important;
  border: none !important;
}

.form-check-input:before {
  content: "";
  position: absolute;
  width: 30px;
  height: 30px;
  top: 2px;
  left: 2px;
  background-color: #fff;
  border-radius: 50%;
  transition: transform .3s ease-in-out;
  box-shadow: 0 1px 4px #0000004d;
}

.form-check-input:checked {
  background-color: #00adef !important;
}

.form-check-input:checked:before {
  transform: translateX(38px);
}

.form-check-input:focus {
  box-shadow: none !important;
}

/*# sourceMappingURL=src_css_Home_Switch_scss_b52d8e88.css.map*/